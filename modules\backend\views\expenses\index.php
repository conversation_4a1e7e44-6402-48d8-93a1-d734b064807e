<?php

use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\web\View;
use app\assets\DataTablesAsset;
use app\assets\Select2Asset;
use app\modules\backend\models\Expenses;

DataTablesAsset::register($this);
Select2Asset::register($this);

$this->title = Yii::t("app", "expenses_section");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
$all = Yii::t("app", "all");

$inactiveText = Yii::t("app", "inactive");
$activeText = Yii::t("app", "active");
$detailText = Yii::t("app", "detail");
$deleteText = Yii::t("app", "delete");

$cashText = Yii::t('app', 'cash');
$cardText = Yii::t('app', 'card');
$transferText = Yii::t('app', 'transfer');

?>

<style>
    /* Стили для индикатора загрузки */
    #expenses-grid-pjax.loading {
        position: relative;
        min-height: 200px;
    }

    #expenses-grid-pjax.loading::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.7);
        z-index: 1000;
    }

    /* Стили для адаптивной таблицы */
    @media (max-width: 768px) {
        #expenses-grid-view {
            display: block;
            width: 100%;
            overflow-x: auto;
        }

        #expenses-grid-view th,
        #expenses-grid-view td {
            white-space: nowrap;
            font-size: 12px;
            padding: 5px;
        }

        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }
    }

    #expenses-grid-pjax.loading::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.7);
        z-index: 1000;
    }

    #expenses-grid-pjax.loading::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 50px;
        height: 50px;
        margin: -25px 0 0 -25px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        z-index: 1001;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Стили для Select2 */
    #expense_type_filter.select2 {
        min-width: 150px !important;
        width: 100% !important;
    }

    #expense_type_filter + .select2-container {
        width: 150px !important;
    }

    /* Стили для полей ввода даты */
    input[type="date"].form-control {
        width: 150px;
    }

    /* Отступы между элементами */
    .d-flex.gap-2 {
        gap: 0.5rem !important;
    }
</style>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-4">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>

        <div class="col-md-8">
            <?php if (Yii::$app->user->can('admin') || Yii::$app->user->can('product_keeper') || Yii::$app->user->can('accountant')): ?>
                <div class="filter-container">
                    <!-- Добавляем стили для адаптивности -->
                    <style>
                        .filter-container {
                            display: flex;
                            flex-wrap: wrap;
                            gap: 8px;
                            justify-content: flex-end;
                            align-items: center;
                            margin-bottom: 10px;
                        }

                        .date-inputs {
                            display: flex;
                            gap: 4px;
                            flex-wrap: wrap;
                        }

                        .date-inputs input {
                            width: 140px;
                        }

                        .filter-buttons {
                            display: flex;
                            gap: 4px;
                            flex-wrap: wrap;
                        }

                        .filter-select {
                            min-width: 150px;
                            max-width: 200px;
                        }

                        @media (max-width: 768px) {
                            .filter-container {
                                justify-content: center;
                            }

                            .date-inputs, .filter-buttons {
                                width: 100%;
                                justify-content: center;
                            }

                            .filter-select {
                                width: 100%;
                                max-width: 100%;
                            }
                        }
                    </style>

                    <!-- Date inputs -->
                    <div class="date-inputs">
                        <input type="date" id="date_from" class="form-control" placeholder="<?= Yii::t('app', 'From Date') ?>">
                        <input type="date" id="date_to" class="form-control" placeholder="<?= Yii::t('app', 'To Date') ?>">
                    </div>

                    <div>
                        <a href="#" id="export-expenses-btn" class="btn btn-success btn-sm">
                            <i class="fas fa-file-excel"></i> <?= Yii::t('app', 'export_excel') ?>
                        </a>
                    </div>

                    <!-- Worker filter dropdown -->
                    <select id="expense_type_filter" class="form-control select2 filter-select">
                        <option value=""><?= Yii::t('app', 'expenses') ?></option>
                        <?php foreach($expenseTypes as $type): ?>
                            <option value="<?= $type->id ?>">
                                <?= Yii::t('app', match ($type->name) {
                                    'Oylik' => 'salary',
                                    'Avans' => 'advance',
                                    'Bonus' => 'bonus',
                                    'Debt' => 'debt',
                                    'One time payment' => 'one_time_payment',
                                    default => $type->name,
                                }) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>

                    <div class="filter-buttons">
                        <!-- Filter button -->
                        <button type="button" class="btn btn-primary btn-sm" id="search-button">
                            <?= Yii::t('app', 'search') ?>
                        </button>
                        <!-- Pay salary button -->
                        <a href="#" class="btn btn-primary btn-sm expenses-create" data-toggle="modal" data-target="#ideal-mini-modal">
                            <?= Yii::t('app', 'add_expense') ?>
                        </a>
                    </div>
                </div>
            <?php endif ?>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'expenses-grid-pjax']); ?>
    <?php if($result): ?>
        <div>
            <table id="expenses-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "expense_type") ?></th>
                        <th><?= Yii::t("app", "added_by") ?></th>
                        <th><?= Yii::t("app", "payment_type") ?></th>
                        <th><?= Yii::t("app", "amount") ?></th>
                        <th><?= Yii::t("app", "expenses_created_at") ?></th>
                        <th><?= Yii::t("app", "description") ?></th>
                        <th><?= Yii::t("app", "status") ?></th>
                        <th><?= Yii::t("app", "actions") ?></th>
                    </tr>
                </thead>
                <tbody>
                <?php foreach ($result as $model): ?>
                    <tr>
                    <td>
                        <?php
                            $expenseMapping = [
                                'Oylik' => 'salary',
                                'Avans' => 'advance',
                                'Bonus' => 'bonus',
                                'Debt' => 'debt',
                                'One time payment' => 'one_time_payment',
                            ];

                            $expenseType = $model['expense_type_name'] ?? null;

                            echo Yii::t('app', match ($expenseType) {
                                'Oylik' => 'salary',
                                'Avans' => 'advance',
                                'Bonus' => 'bonus',
                                'Debt' => 'debt',
                                'One time payment' => 'one_time_payment',
                                default => $expenseType,
                            });
                        ?>
                    </td>
                        <td><?= Html::encode($model['added_by_user']) ?></td>
                        <td>
                            <?= Html::encode(Expenses::getTypePayment($model['payment_type'])) ?>
                        </td>
                        <td><?= number_format($model['summa'], 0, '.', ',') ?></td>
                        <td><?= date("d.m.Y h:i", strtotime($model['created_at'])) ?></td>
                        <td><?= Html::encode($model['description']) ?></td>
                        <td>
                            <?php if ($model['status'] == Expenses::TYPE_NOT_ACCEPTED): ?>
                                <span class="badge badge-danger">
                                    <?= Yii::t("app", "not_accepted") ?>
                                </span>
                            <?php else: ?>
                                <span class="badge badge-success">
                                    <?= Yii::t("app", "accepted") ?>
                                </span>
                            <?php endif; ?>
                        </td>

                        <td>
                                <?php
                                    // Используем метод canBeEdited для определения, показывать ли кнопки
                                    $showButton = Expenses::canBeEdited($model['id']);
                                ?>
                              <?php if ($showButton): ?>
                                    <div class="dropdown d-inline">
                                        <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                            <?php echo Yii::t("app", "detail"); ?>
                                        </a>
                                        <div class="dropdown-menu">
                                            <a href="#" class="dropdown-item expenses-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                                <?= Yii::t("app", "update") ?>
                                            </a>
                                            <a href="#" class="dropdown-item expenses-delete" data-toggle="modal" data-target="#ideal-mini-modal-delete" data-id="<?= Html::encode($model['id']) ?>">
                                                <?= Yii::t("app", "delete") ?>
                                            </a>
                                        </div>
                                    </div>
                               <?php endif; ?>
                        </td>

                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>

<div id="one" data-text="<?= Yii::t("app", "add_expense") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "expense_update") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "expense_delete") ?>"></div>

<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var three = $('#three').attr('data-text');

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";
    var all = "{$all}";
    var detailText = "{$detailText}";
    var deleteText = "{$deleteText}";
    var activeText = "{$activeText}";
    var inactiveText = "{$inactiveText}";
    var cashText = "{$cashText}";
    var cardText = "{$cardText}";
    var transferText = "{$transferText}";

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#expenses-grid-view')) {
            $('#expenses-grid-view').DataTable().destroy();
        }

        $('#expenses-grid-view').DataTable({
            "language": {
                "search": searchLabel,
                "lengthMenu": lengthMenuLabel,
                "zeroRecords": zeroRecordsLabel,
                "info": infoLabel,
                "infoEmpty": infoEmptyLabel,
                "infoFiltered": infoFilteredLabel
            },
            "pageLength": 50,
            "order": [[4, 'desc']],
            "columnDefs": [
                {
                    "targets": [2, 6, 7],
                    "orderable": false
                },
                {
                    "targets": 4,
                    "type": "date-eu",
                    "render": function(data, type, row) {
                        if (type === 'sort') {
                            var parts = data.split(' ');
                            var dateParts = parts[0].split('.');
                            return dateParts[2] + dateParts[1] + dateParts[0] + parts[1].replace(':', '');
                        }
                        return data;
                    }
                }
            ]
        });
    }

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }

    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "Натижа топилмади";
                }
            }
        });
    }

    function initializeExpensesCreate() {
        $(document).off('click.expenses-create').on('click.expenses-create', '.expenses-create', function() {
            $.ajax({
                url: '/backend/expenses/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("expenses-create-button");
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.expenses-create-button').on('click.expenses-create-button', '.expenses-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#expenses-form').serialize();
                $.ajax({
                    url: '/backend/expenses/create',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        button.prop('disabled', false); // Разблокируем кнопку

                        if (response.status === 'success') {
                            // Если успешно, закрываем модальное окно и обновляем таблицу
                            $('#ideal-mini-modal').modal('hide');
                            $.pjax.reload({ container: '#expenses-grid-pjax' });
                        } else if (response.status === 'error') {
                            // Очищаем все предыдущие ошибки
                            $('.help-block').text('').hide(); // Очищаем ошибки полей
                            $('#form-error-message').text('').hide(); // Очищаем общее сообщение об ошибке

                            if (response.errors) {
                                // Если есть ошибки валидации, отображаем их
                                $.each(response.errors, function(field, messages) {
                                    var fieldElement = $('#expenses-form').find('[name="Expenses[' + field + ']"]');
                                    var errorContainer = fieldElement.closest('.form-group').find('.help-block');

                                    if (fieldElement.length && errorContainer.length) {
                                        errorContainer.text(messages.join(', ')).show();
                                    }
                                });
                            }

                            if (response.message) {
                                // Если есть общее сообщение об ошибке, отображаем его
                                $('#form-error-message').text(response.message).css('color', 'red').show();
                            }
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });

        $(document).off('keypress.expenses-create-button').on('keypress.expenses-create-button', '#expenses-form', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                $('.expenses-create-button').trigger('click');
            }
        });
    }

    function initializeExpensesDelete() {
        $(document).off('click.expenses-delete').on('click.expenses-delete', '.expenses-delete', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/expenses/delete',
                data: {id: id},
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal-delete .modal-title').html(three);
                    $('#ideal-mini-modal-delete .modal-body').html(response.content);
                    $('#ideal-mini-modal-delete .mini-button').addClass("expenses-delete-button");
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.expenses-delete-button').on('click.expenses-delete-button', '.expenses-delete-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#expenses-delete-form').serialize();
                $.ajax({
                    url: '/backend/expenses/delete',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        button.prop('disabled', false);
                        if (response.status === 'success') {
                            $('#ideal-mini-modal-delete').modal('hide');
                            $.pjax.reload({container: '#expenses-grid-pjax'});
                        } else {
                            $('#expenses-form').yiiActiveForm('updateMessages', response.errors, true);
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });

        $(document).off('keypress.expenses-delete-button').on('keypress.expenses-delete-button', '#expenses-delete-form', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                $('.expenses-delete-button').trigger('click');
            }
        });
    }

    function initializeSearch() {
        $('#search-button').on('click', function() {
            var startDate = $('#date_from').val();
            var endDate = $('#date_to').val();
            var expenseTypeId = $('#expense_type_filter').val();

            // Добавляем индикатор загрузки
            $('#search-button').prop('disabled', true);
            $('#expenses-grid-pjax').addClass('loading');

            $.ajax({
                url: '/backend/expenses/search',
                type: 'POST',
                data: {
                    start_date: startDate || null,
                    end_date: endDate || null,
                    expense_type_id: expenseTypeId || null
                },
                success: function(response) {
                    $('#expenses-grid-pjax').html(response);
                    initializeDataTable();
                    initializeDropdown();
                    initializeSelect2(); // Инициализация Select2 после обновления таблицы

                },
                error: function(xhr, status, error) {
                    console.error('Search error:', error);
                    iziToast.error({
                        timeout: 5000,
                        icon: 'fas fa-exclamation-triangle',
                        message: '<?= Yii::t("app", "An error occurred while searching") ?>',
                        position: 'topRight',
                        onOpening: function(instance, toast) {
                            toast.style.top = (parseInt(toast.style.top, 10) + 50) + 'px';
                        }
                    });
                },
                complete: function() {
                    $('#search-button').prop('disabled', false);
                    $('#expenses-grid-pjax').removeClass('loading');
                }
            });
        });
    }

    function initializeDirectExcelExport() {
        $(document).off('click.export-expenses').on('click.export-expenses', '#export-expenses-btn', function(e) {
            e.preventDefault();

            var startDate = $('#date_from').val();
            var endDate = $('#date_to').val();
            var expenseTypeId = $('#expense_type_filter').val();

            if (!startDate || !endDate) {
                 iziToast.warning({
                    message: 'Санани танланг!',
                    position: 'topRight',
                    timeout: 5000,
                    icon: 'fas fa-exclamation-triangle',
                    onOpening: function(instance, toast) {
                            toast.style.top = (parseInt(toast.style.top, 10) + 50) + 'px';
                        }
                });
                return;
            }

            var form = $('<form>')
                .attr('method', 'post')
                .attr('action', '/backend/expenses/expenses-excel')
                .css('display', 'none');

            $('<input>').attr({ type: 'hidden', name: 'start_date', value: startDate }).appendTo(form);
            $('<input>').attr({ type: 'hidden', name: 'end_date', value: endDate }).appendTo(form);
            if (expenseTypeId) {
                 $('<input>').attr({ type: 'hidden', name: 'expense_type_id', value: expenseTypeId }).appendTo(form);
            }

            var csrfToken = $('meta[name="csrf-token"]').attr("content");
            $('<input>').attr({ type: 'hidden', name: '_csrf', value: csrfToken }).appendTo(form);

            $('body').append(form);
            form.submit();
            form.remove();
         });
     }

    $(document).off('pjax:end').on('pjax:end', function() {
        initializeDataTable();
        initializeDropdown();
        initializeSelect2();
    });

    initializeDataTable();
    initializeDropdown();
    initializeExpensesCreate();
    initializeExpensesUpdate();
    initializeExpensesDelete();
    initializeDirectExcelExport();
    initializeSearch();

    function initializeExpensesUpdate() {
        $(document).off('click.expenses-update').on('click.expenses-update', '.expenses-update', function() {
            var id = $(this).attr("data-id");
            $.ajax({
                url: '/backend/expenses/update',
                data: {id: id},
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#ideal-mini-modal .modal-title').html(two);
                        $('#ideal-mini-modal .modal-body').html(response.content);
                        $('#ideal-mini-modal .mini-button').addClass("expenses-update-button");

                        // Инициализация Select2 для формы обновления
                        $('#ideal-mini-modal select.select2').select2({
                            dropdownParent: $('#ideal-mini-modal'),
                            width: '100%'
                        });

                        // Инициализация форматирования числовых полей
                        $('.formatted-numeric-input').on('input', function() {
                            var value = $(this).val().replace(/\s+/g, '');
                            var formattedValue = value.replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
                            $(this).val(formattedValue);
                        });
                    } else if (response.status === 'error') {
                        iziToast.error({
                            timeout: 5000,
                            icon: 'fas fa-exclamation-triangle',
                            message: response.message,
                            position: 'topRight',
                            onOpening: function(instance, toast) {
                                toast.style.top = (parseInt(toast.style.top, 10) + 50) + 'px';
                            }
                        });
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.expenses-update-button').on('click.expenses-update-button', '.expenses-update-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#expenses-update-form').serialize();
                $.ajax({
                    url: '/backend/expenses/update',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        button.prop('disabled', false);
                        if (response.status === 'success') {
                            $('#ideal-mini-modal').modal('hide');
                            $.pjax.reload({container: '#expenses-grid-pjax'});

                            iziToast.success({
                                timeout: 5000,
                                icon: 'fas fa-check-circle',
                                message: response.message,
                                position: 'topRight',
                                onOpening: function(instance, toast) {
                                    toast.style.top = (parseInt(toast.style.top, 10) + 50) + 'px';
                                }
                            });
                        } else if (response.status === 'error') {
                            if (response.errors) {
                                // Отображаем ошибки валидации
                                $.each(response.errors, function(field, errors) {
                                    var errorContainer = $('#' + field.toLowerCase() + '-error');
                                    errorContainer.html(errors.join('<br>'));
                                });
                            } else {
                                iziToast.error({
                                    timeout: 5000,
                                    icon: 'fas fa-exclamation-triangle',
                                    message: response.message,
                                    position: 'topRight',
                                    onOpening: function(instance, toast) {
                                        toast.style.top = (parseInt(toast.style.top, 10) + 50) + 'px';
                                    }
                                });
                            }
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
