<?php

use yii\helpers\Html;
use yii\widgets\ActiveForm;

/* @var $this yii\web\View */
/* @var $reportData array */
/* @var $startDate string */
/* @var $endDate string */

$this->title = Yii::t('app', 'finance_report');

?>

<style>
    .text-right {
        text-align: right;
    }
    .profit-positive {
        color: #28a745;
        font-weight: bold;
    }
    .profit-negative {
        color: #dc3545;
        font-weight: bold;
    }
.no-wrap {
        white-space: nowrap;
    }
    /* убираем глобальный горизонтальный скролл, пусть таблица скроллится внутри контейнера */
    .table-responsive {
        overflow-x: auto;
    }
    html, body {
        max-width: 100%;
        overflow-x: hidden;
    }
</style>

<div class="row align-items-center mb-3">
    <div class="col-md-6">
        <h3 class="mb-0"><?= Yii::t('app', 'finance_report') ?></h3>
    </div>
    <div class="col-md-6 d-flex justify-content-end">
        <?php $form = ActiveForm::begin([
            'method' => 'get',
            'action' => ['/backend/report/finance'],
            'options' => ['class' => 'd-inline-flex align-items-center']
        ]); ?>
            <input type="date" 
                   name="start_date" 
                   class="form-control mr-2" 
                   style="width: 160px;"
                   value="<?= $startDate ?? date('Y-m-d') ?>" 
                   placeholder="<?= Yii::t('app', 'From Date') ?>">
            <input type="date" 
                   name="end_date" 
                   class="form-control mr-2" 
                   style="width: 160px;"
                   value="<?= $endDate ?? date('Y-m-d') ?>" 
                   placeholder="<?= Yii::t('app', 'To Date') ?>">
            <?= Html::submitButton(Yii::t('app', 'search'), ['class' => 'btn btn-primary']) ?>
        <?php ActiveForm::end(); ?>
    </div>
</div>
<?php
    // Формируем комбинированный отчёт Приход/Расход
    $incomeItems   = $reportData['income']['items']   ?? [];
    $expenseItems  = $reportData['expenses']['items'] ?? [];
    $rowsCount = max(count($incomeItems), count($expenseItems));
?>
<div class="card">
    <div class="card-body p-0">
        <div>
            <table class="table table-bordered table-striped table-hover mb-0 no-dt" style="min-width:100%;table-layout:fixed;">
                <thead class="thead-light text-center">
                    <tr>
                        <th colspan="5"><?= Yii::t('app', 'income') ?></th>
                        <th colspan="3"><?= Yii::t('app', 'expenses') ?></th>
                    </tr>
                    <tr>
                        <th><?= Yii::t('app', 'payment_date') ?></th>
                        <th><?= Yii::t('app', 'client') ?></th>
                        <th><?= Yii::t('app', 'amount') ?></th>
                        <th><?= Yii::t('app', 'payment_type') ?></th>
                        <th><?= Yii::t('app', 'cashier') ?></th>
                        <th><?= Yii::t('app', 'expense_type') ?></th>
                        <th><?= Yii::t('app', 'amount') ?></th>
                        <th><?= Yii::t('app', 'payment_type') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php for ($i = 0; $i < $rowsCount; $i++): ?>
                        <?php $inc = $incomeItems[$i]  ?? null; ?>
                        <?php $exp = $expenseItems[$i] ?? null; ?>
                        <tr>
                            <td><?= $inc ? Yii::$app->formatter->asDatetime($inc['created_at'], 'php:d.m.Y H:i') : '&nbsp;' ?></td>
                            <td><?= $inc ? Html::encode($inc['client_name'] ?? '-') : '&nbsp;' ?></td>
                            <td class="text-right">
                                <?= $inc ? Yii::$app->formatter->asDecimal($inc['amount'], 0) : '&nbsp;' ?>
                            </td>
                            <td><?= $inc ? Html::encode($inc['payment_type'] ?? '-') : '&nbsp;' ?></td>
                            <td><?= $inc ? Html::encode($inc['cashier_name'] ?? '-') : '&nbsp;' ?></td>
                            <td><?= $exp ? Html::encode($exp['expense_type'] ?? '-') : '&nbsp;' ?></td>
                            <td class="text-right">
                                <?php
                                    $currency = $exp['currency'] ?? 'UZS';
                                    $currency = $currency ? strtoupper($currency) : 'UZS';
                                ?>
                                <?= $exp ? Yii::$app->formatter->asDecimal($exp['amount'], 0) . ' ' . Html::encode($currency) : '&nbsp;' ?>
                            </td>
                            <td><?= $exp ? Html::encode($exp['payment_type'] ?? '-') : '&nbsp;' ?></td>
                        </tr>
                    <?php endfor; ?>
                </tbody>
                <tfoot class="bg-light font-weight-bold">
    <tr>
        <td class="text-right" colspan="6">Итого (UZS):</td>
        <td class="text-right">
            <?= Yii::$app->formatter->asDecimal($reportData['expenses']['total_som'] ?? 0, 0) ?> UZS
        </td>
        <td></td>
    </tr>
    <tr>
        <td class="text-right" colspan="6">Итого (USD):</td>
        <td class="text-right">
            <?= Yii::$app->formatter->asDecimal($reportData['expenses']['total_usd'] ?? 0, 2) ?> USD
        </td>
        <td></td>
    </tr>
    <tr>
        <td class="text-right" colspan="2"><?= Yii::t('app', 'total') ?>:</td>
        <td class="text-right"><?= Yii::$app->formatter->asDecimal($reportData['income']['total'] ?? 0, 0) ?></td>
        <td></td>
        <td></td>
        <td class="text-right">&nbsp;</td>
        <td class="text-right">&nbsp;</td>
        <td></td>
    </tr>
    
</tfoot>
            </table>
        </div>
    </div>
</div>
<?php if (empty($incomeItems) && empty($expenseItems)): ?>
    <div class="alert alert-info text-center mt-3">
        <i class="fas fa-info-circle mr-2"></i>
        <?= Yii::t('app', 'no_data_found') ?>
    </div>
<?php endif; ?>
<script>
   document.querySelectorAll('input[type="date"]').forEach(input => {
    input.addEventListener('input', function(e) {
        let value = e.target.value;
        
        if (value) {
            let [year, month, day] = value.split('-');
            
            if (year.length > 4) {
                year = year.slice(0, 4);
                e.target.value = `${year}-${month}-${day}`;
            }
            
            if (parseInt(year) > 9999) {
                e.target.value = `9999-${month}-${day}`;
            }
        }
    });
});
</script>