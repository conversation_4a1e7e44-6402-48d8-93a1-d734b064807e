<?php
use yii\helpers\Html;
$calculatedTotalSum = 0;
?>

<div class="invoice-view">
    <!-- Кнопка печатать накладную -->
    <div class="text-right mb-3">
        <button type="button" class="btn btn-primary print-invoice-btn" data-id="<?= $model->id ?>">
            <i class="fas fa-print"></i> <?= Yii::t('app', 'print_invoice') ?>
        </button>
    </div>

    <div class="card-body">
        <h6 style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'Invoice Information') ?></h6>

        <table class="table table-bordered table-hover">
            <thead>
                <tr>
                    <th><?= Yii::t('app', 'client') ?></th>
                    <th><?= Yii::t('app', 'driver') ?></th>
                    <th><?= Yii::t('app', 'car_number') ?></th>
                    <th><?= Yii::t('app', 'created_at') ?></th>
                    <th><?= Yii::t('app', 'sell_user') ?></th>
                    <?php if ($model->confirm_user_id): ?>
                        <th><?= Yii::t('app', 'confirm_user') ?></th>
                    <?php endif; ?>
                    <th><?= Yii::t('app', 'status') ?></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><?= Html::encode($model->client->full_name) ?></td>
                    <td><?= Html::encode($model->driver) ?></td>
                    <td><?= Html::encode($model->car_number) ?></td>
                    <td><?= date("d.m.Y", strtotime($model->created_at)) ?></td>
                    <td><?= Html::encode($model->sellUser->username) ?></td>
                    <?php if ($model->confirm_user_id): ?>
                        <td><?= Html::encode($model->confirmUser->username) ?></td>
                    <?php endif; ?>
                    <td>
                        <span class="<?= $model->deleted_at == NULL ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                            <?= $model->deleted_at == NULL ? Yii::t("app", "active") : Yii::t("app", "inactive") ?>
                        </span>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Детали продуктов -->
    <div class="card-body">
        <h6 style="margin-bottom: -0.5rem;"><?= Yii::t('app', 'products') ?></h6>

        <div class="table-responsive">
            <table class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>#</th>
                        <th><?= Yii::t('app', 'product') ?></th>
                        <th><?= Yii::t('app', 'quantity') ?></th>
                        <th><?= Yii::t('app', 'price') ?></th>
                        <th><?= Yii::t('app', 'all') ?></th>
                        <th><?= Yii::t('app', 'status') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($model->salesDetails as $index => $detail): ?>
                        <tr>
                            <td><?= $index + 1 ?></td>
                            <td><?= Html::encode($detail->product->name) ?></td>
                            <td><?= Html::encode($detail->quantity) ?></td>
                            <td><?= number_format($detail->special_price, 0, '.', ' ') ?></td>
                            <td><?= number_format($detail->quantity * $detail->special_price, 0, '.', ' ') ?></td>
                            <td>
                                <span class="<?= $detail->deleted_at == NULL ? 'badge badge-outline-success' : 'badge badge-danger' ?>">
                                    <?= $detail->deleted_at == NULL ? Yii::t("app", "active") : Yii::t("app", "inactive") ?>
                                </span>
                            </td>
                        </tr>
                          <?php
                            if ($detail->deleted_at == NULL) {
                                $quantity = is_numeric($detail->quantity) ? $detail->quantity : 0;
                                $price = is_numeric($detail->special_price) ? $detail->special_price : 0;
                                $calculatedTotalSum += $quantity * $price;
                            }
                            ?>
                    <?php endforeach; ?>

                    <?php if (!empty($model->bonus)): ?>
                        <tr>
                            <td colspan="6" class="bg-light"><strong><?= Yii::t('app', 'bonus_products') ?></strong></td>
                        </tr>
                        <?php foreach ($model->bonus as $bonus): ?>
                            <tr class="bonus-row">
                                <td colspan="2"><?= $bonus->product->name ?></td>
                                <td><?= $bonus->quantity ?></td>
                                <td colspan="3"></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="4" class="text-right"><strong><?= Yii::t('app', 'total_sum') ?>:</strong></td>
                        <td colspan="2"><strong><?= number_format($calculatedTotalSum, 0, '.', ' ') ?></strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>




    </div>
</div>
