(function ($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var three = $('#three').attr('data-text');
    var four = $('#four').attr('data-text');
    var five = $('#five').attr('data-text');
    var six = $('#six').attr('data-text');
    var seven = $('#seven').attr('data-text');

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#part-grid-view')) {
            $('#part-grid-view').DataTable().destroy();
        }

        $('#part-grid-view').DataTable({
            "language": {
                "search": datatablesI18n.search,
                "lengthMenu": datatablesI18n.lengthMenu,
                "zeroRecords": datatablesI18n.zeroRecords,
                "info": datatablesI18n.info,
                "infoEmpty": datatablesI18n.infoEmpty,
                "infoFiltered": datatablesI18n.infoFiltered
            },
            "pageLength": 50,
            "columnDefs": [
                {
                    "targets": [0, 6],
                    "orderable": false
                },
                {
                    "targets": 3,
                    "render": function (data, type, row) {
                        if (type === 'display' && data !== '-') {
                            return parseFloat(data).toLocaleString('en-US', { maximumFractionDigits: 2 });
                        }
                        return data;
                    }
                },
                {
                    "targets": 4,
                    "render": function (data, type, row) {
                        if (type === 'display' && data !== '-' && data) {
                            return moment(data).format('DD.MM.YYYY');
                        }
                        return data;
                    }
                }
            ]
        });
    }

    function initializeSearch() {
        $('#search-button').on('click', function () {
            var status = $('#part_filter').val();
            var sourceType = $('#source_type_filter').val();
            var equipmentId = $('#equipment_filter').val();

            $.ajax({
                url: equipmentPartUrls.search,
                type: 'POST',
                data: {
                    status: status,
                    source_type: sourceType,
                    equipment_id: equipmentId
                },
                success: function (response) {
                    if (response.status === 'success') {
                        $('#part-grid-pjax').html(response.content);
                        initializeDataTable();
                    } else {
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Search error:', error);
                    alert("<?= Yii::t('app', 'Error occurred while searching') ?>");
                }
            });
        });
    }

    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            language: {
                noResults: function () {
                    return "<?= Yii::t('app', 'No results found') ?>";
                },
                searching: function () {
                    return "<?= Yii::t('app', 'Searching...') ?>";
                }
            },
            allowClear: true,
            placeholder: function () {
                return $(this).data('placeholder');
            }
        });
    }

    function initializeActionButtons() {
        // Кнопка "Архивировать"
        $('#btn-archive').on('click', function (e) {
            e.preventDefault();
            alert('<?= Yii::t("app", "Archive functionality will be implemented in the future") ?>');
        });

        // Кнопка "Поделиться"
        $('#btn-share').on('click', function (e) {
            e.preventDefault();
            alert('<?= Yii::t("app", "Share functionality will be implemented in the future") ?>');
        });
    } function initializeAll() {
        initializeDataTable();
        initializeSelect2();
        initializePartCreate();
        initializePartUpdate();
        initializePartPhotos();
        initializePartPhoto();
        initializePartStatus();
        initializePartAttach();
        initializePartDetach();
        initializePartIncome();
        initializePartDefect();
        initializePartWriteOff();
        initializeActionButtons();
        initializeSearch();
        initializeDropdown();
    }

    // Функция для получения параметров из URL
    function getUrlParameter(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
        var results = regex.exec(location.search);
        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    }

    // Функция для очистки ошибок валидации
    function clearValidationErrors() {
        $('.form-control').removeClass('is-invalid');
        $('.invalid-feedback').hide().html('');
    }

    // Функция для отображения ошибок валидации
    function displayValidationErrors(errors) {
        clearValidationErrors();
        if (errors) {
            $.each(errors, function (field, messages) {
                var input = $('[name="' + field + '"]');
                var errorContainer = $('#' + field + '-error');

                if (input.length && errorContainer.length) {
                    input.addClass('is-invalid');
                    errorContainer.html(Array.isArray(messages) ? messages.join('<br>') : messages).show();
                }
            });
        }
    }

    // Initialize everything on first load
    initializeAll();

    // Если в URL есть параметр equipment_id, устанавливаем соответствующее значение в фильтре
    var equipmentIdParam = getUrlParameter('equipment_id');

    if (equipmentIdParam) {
        setTimeout(function () {
            $('#equipment_filter').val(equipmentIdParam).trigger('change');
            // Запускаем поиск с выбранным фильтром
            $('#search-button').trigger('click');
        }, 500); // Небольшая задержка для уверенности, что select2 инициализирован
    }

    // Re-initialize after PJAX reloads
    $(document).on('pjax:success', function () {
        initializeAll();

        // После перезагрузки через PJAX снова проверяем параметр в URL
        var equipmentIdParam = getUrlParameter('equipment_id');

        if (equipmentIdParam) {
            setTimeout(function () {
                $('#equipment_filter').val(equipmentIdParam).trigger('change');
                // Запускаем поиск с выбранным фильтром
                $('#search-button').trigger('click');
            }, 500); // Небольшая задержка для уверенности, что select2 инициализирован
        }
    });

    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function (e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item:not([href^="/"])', function (e) {
            e.preventDefault();
            e.stopPropagation();
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function (e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }

    function initializePartCreate() {
        $(document).off('click.part-create').on('click.part-create', '.part-create', function () {
            $.ajax({
                url: equipmentPartUrls.create,
                dataType: 'json',
                type: 'GET',
                success: function (response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("part-create-button");
                    initializeSelect2();
                },
                error: function (xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.part-create-button').on('click.part-create-button', '.part-create-button', function () {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                clearValidationErrors();
                var formData = new FormData($('#part-create-form')[0]);
                $.ajax({
                    url: equipmentPartUrls.create,
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#part-grid-pjax',
                                complete: function () {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                            if (response.errors) {
                                displayValidationErrors(response.errors);
                            }
                        }
                    },
                    error: function (xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

    function initializePartUpdate() {
        $(document).off('click.part-update').on('click.part-update', '.part-update', function () {
            var id = $(this).data('id');
            $.ajax({
                url: equipmentPartUrls.update,
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function (response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("part-update-button");
                    initializeSelect2();
                },
                error: function (xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.part-update-button').on('click.part-update-button', '.part-update-button', function () {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                clearValidationErrors();
                var formData = new FormData($('#part-update-form')[0]);
                $.ajax({
                    url: equipmentPartUrls.update,
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#part-grid-pjax',
                                complete: function () {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                            if (response.errors) {
                                displayValidationErrors(response.errors);
                            }
                        }
                    },
                    error: function (xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    } function initializePartPhoto() {
        $(document).off('click.part-photo');

        $(document).on('click.part-photo', '.part-photo', function () {
            var id = $(this).data('id');
            $.ajax({
                url: equipmentPartUrls.getPhoto,
                type: 'GET',
                data: { id: id },
                success: function (response) {
                    if (response.status === 'success' && response.photos) {
                        $('#ideal-large-modal-without-save .modal-title').text(response.name);

                        // Создаем карусель в модальном окне
                        var carouselHtml = createPhotoCarousel(response.photos);
                        $('#ideal-large-modal-without-save .modal-body').html(carouselHtml);
                        $('#ideal-large-modal-without-save').modal('show');

                        // Инициализируем навигацию в модальном окне
                        initializeModalCarousel();
                    }
                },
                error: function () {
                    console.error('Error loading photo');
                }
            });
        });        // Очистка содержимого при закрытии
        $('#ideal-large-modal-without-save').on('hidden.bs.modal', function () {
            $('#ideal-large-modal-without-save .modal-body').html('');
            $('#ideal-large-modal-without-save .modal-title').text('');
            // Удаляем обработчики клавиатуры
            $(document).off('keydown.modal-carousel');
        });
    }

    function createPhotoCarousel(photos) {
        if (photos.length === 1) {
            // Если только одна фотография, показываем просто
            return '<div class="text-center"><img src="' + photos[0].url + '" alt="" style="max-width: 100%; max-height: 70vh;"></div>';
        }

        var html = '<div class="modal-photo-carousel" style="position: relative;">';
        html += '<div class="carousel-container" style="text-align: center; position: relative;">';

        // Добавляем все фотографии
        photos.forEach(function (photo, index) {
            var display = index === 0 ? 'block' : 'none';
            html += '<img src="' + photo.url + '" alt="Photo ' + (index + 1) + '" class="modal-carousel-photo" data-index="' + index + '" ';
            html += 'style="max-width: 100%; max-height: 70vh; display: ' + display + ';">';
        });

        // Добавляем кнопки навигации
        if (photos.length > 1) {
            html += '<button type="button" class="modal-carousel-btn modal-carousel-prev" ';
            html += 'style="position: absolute; left: 20px; top: 50%; transform: translateY(-50%); ';
            html += 'background: rgba(0,0,0,0.8); color: white; border: none; border-radius: 50%; ';
            html += 'width: 50px; height: 50px; font-size: 24px; cursor: pointer; z-index: 10;">‹</button>';

            html += '<button type="button" class="modal-carousel-btn modal-carousel-next" ';
            html += 'style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%); ';
            html += 'background: rgba(0,0,0,0.8); color: white; border: none; border-radius: 50%; ';
            html += 'width: 50px; height: 50px; font-size: 24px; cursor: pointer; z-index: 10;">›</button>';
        }

        html += '</div>';

        // Добавляем счетчик фотографий
        if (photos.length > 1) {
            html += '<div class="photo-counter" style="text-align: center; margin-top: 15px; font-size: 16px;">';
            html += '<span class="current-modal-photo">1</span> / <span class="total-modal-photos">' + photos.length + '</span>';
            html += '</div>';

            // Добавляем миниатюры (опционально)
            html += '<div class="photo-thumbnails" style="text-align: center; margin-top: 15px; overflow-x: auto;">';
            photos.forEach(function (photo, index) {
                var activeClass = index === 0 ? 'active' : '';
                html += '<img src="' + photo.url + '" class="thumbnail ' + activeClass + '" data-index="' + index + '" ';
                html += 'style="width: 60px; height: 60px; object-fit: cover; margin: 0 5px; cursor: pointer; ';
                html += 'border: 2px solid ' + (index === 0 ? '#007bff' : 'transparent') + '; border-radius: 4px;">';
            });
            html += '</div>';
        }

        html += '</div>';
        return html;
    }

    function initializeModalCarousel() {
        // Обработчик для кнопок навигации
        $('.modal-carousel-btn').off('click').on('click', function () {
            var photos = $('.modal-carousel-photo');
            var currentIndex = 0;

            // Находим текущую видимую фотографию
            photos.each(function (index) {
                if ($(this).is(':visible')) {
                    currentIndex = index;
                    return false;
                }
            });

            var totalPhotos = photos.length;
            var newIndex;

            if ($(this).hasClass('modal-carousel-next')) {
                newIndex = (currentIndex + 1) % totalPhotos;
            } else {
                newIndex = (currentIndex - 1 + totalPhotos) % totalPhotos;
            }

            showModalPhoto(newIndex);
        });

        // Обработчик для миниатюр
        $('.thumbnail').off('click').on('click', function () {
            var index = $(this).data('index');
            showModalPhoto(index);
        });

        // Навигация с клавиатуры
        $(document).off('keydown.modal-carousel').on('keydown.modal-carousel', function (e) {
            if ($('#ideal-large-modal-without-save').hasClass('show')) {
                if (e.keyCode === 37) { // Стрелка влево
                    $('.modal-carousel-prev').click();
                } else if (e.keyCode === 39) { // Стрелка вправо
                    $('.modal-carousel-next').click();
                }
            }
        });
    }

    function showModalPhoto(index) {
        var photos = $('.modal-carousel-photo');
        var thumbnails = $('.thumbnail');

        // Скрываем все фотографии и показываем нужную
        photos.hide();
        $(photos[index]).show();

        // Обновляем активную миниатюру
        thumbnails.removeClass('active').css('border-color', 'transparent');
        $(thumbnails[index]).addClass('active').css('border-color', '#007bff');

        // Обновляем счетчик
        $('.current-modal-photo').text(index + 1);
    }

    function initializePartPhotos() {
        $(document).off('click.part-photos').on('click.part-photos', '.part-photos', function () {
            var id = $(this).data('id');
            $.ajax({
                url: equipmentPartUrls.photos,
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function (response) {
                    $('#ideal-large-modal .modal-title').html('<i class="fas fa-images"></i> ' + '<?= Yii::t("app", "Manage Photos") ?>');
                    $('#ideal-large-modal .modal-body').html(response.content);
                    $('#ideal-large-modal').modal('show');
                },
                error: function (xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                    toastr.error('<?= Yii::t("app", "Error loading photos") ?>');
                }
            });
        });

        // Очистка содержимого при закрытии
        $('#ideal-large-modal').on('hidden.bs.modal', function () {
            $('#ideal-large-modal .modal-body').html('');
        });
    }

    function initializePartStatus() {
        $(document).off('click.part-change-status').on('click.part-change-status', '.part-change-status', function () {
            var id = $(this).data('id');
            $.ajax({
                url: equipmentPartUrls.changeStatus,
                type: 'GET',
                data: { id: id },
                success: function (response) {
                    $('#ideal-mini-modal .modal-title').html(two);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('part-status-save');
                    initializeSelect2();
                },
                error: function (xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.part-status-save').on('click.part-status-save', '.part-status-save', function () {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var id = $('.part-change-status').data('id');
                var formData = new FormData($('#part-status-form')[0]);

                $.ajax({
                    url: equipmentPartUrls.changeStatus + '?id=' + id,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#part-grid-pjax',
                                complete: function () {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                        }
                    },
                    error: function (xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

    function initializePartAttach() {
        $(document).off('click.part-attach').on('click.part-attach', '.part-attach', function () {
            var id = $(this).data('id');
            $.ajax({
                url: equipmentPartUrls.attach,
                type: 'GET',
                data: { id: id },
                success: function (response) {
                    $('#ideal-mini-modal .modal-title').html(four);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('part-attach-save');
                    initializeSelect2();
                },
                error: function (xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.part-attach-save').on('click.part-attach-save', '.part-attach-save', function () {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var id = $('.part-attach').data('id');
                var formData = new FormData($('#part-attach-form')[0]);

                $.ajax({
                    url: equipmentPartUrls.attach + '?id=' + id,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#part-grid-pjax',
                                complete: function () {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                        }
                    },
                    error: function (xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

    function initializePartDetach() {
        $(document).off('click.part-detach').on('click.part-detach', '.part-detach', function () {
            var id = $(this).data('id');
            $.ajax({
                url: equipmentPartUrls.detach,
                type: 'GET',
                data: { id: id },
                success: function (response) {
                    $('#ideal-mini-modal .modal-title').html(five);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('part-detach-save');
                    initializeSelect2();
                },
                error: function (xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.part-detach-save').on('click.part-detach-save', '.part-detach-save', function () {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var id = $('.part-detach').data('id');
                var formData = new FormData($('#part-detach-form')[0]);

                $.ajax({
                    url: equipmentPartUrls.detach + '?id=' + id,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#part-grid-pjax',
                                complete: function () {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                        }
                    },
                    error: function (xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

    function initializePartIncome() {
        $(document).off('click.part-income').on('click.part-income', '.part-income', function () {
            var id = $(this).data('id');
            $.ajax({
                url: equipmentPartUrls.income,
                type: 'GET',
                data: { id: id },
                success: function (response) {
                    $('#ideal-mini-modal .modal-title').html(six);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('part-income-save');
                    initializeSelect2();
                },
                error: function (xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.part-income-save').on('click.part-income-save', '.part-income-save', function () {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                clearValidationErrors();
                var formData = new FormData($('#part-income-form')[0]);

                $.ajax({
                    url: equipmentPartUrls.income,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#part-grid-pjax',
                                complete: function () {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                            if (response.errors) {
                                displayValidationErrors(response.errors);
                            }
                        }
                    },
                    error: function (xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

    function initializePartDefect() {
        $(document).off('click.part-defect').on('click.part-defect', '.part-defect', function () {
            var id = $(this).data('id');
            $.ajax({
                url: equipmentPartUrls.defect,
                type: 'GET',
                data: { id: id },
                success: function (response) {
                    $('#ideal-mini-modal .modal-title').html(seven);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('part-defect-save');
                    initializeSelect2();
                },
                error: function (xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.part-defect-save').on('click.part-defect-save', '.part-defect-save', function () {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                clearValidationErrors();
                var formData = new FormData($('#part-defect-form')[0]);

                $.ajax({
                    url: equipmentPartUrls.defect,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#part-grid-pjax',
                                complete: function () {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                            if (response.errors) {
                                displayValidationErrors(response.errors);
                            }
                        }
                    },
                    error: function (xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }

    function initializePartPhotos() {
        $(document).off('click.part-photos').on('click.part-photos', '.part-photos', function () {
            var id = $(this).data('id');
            $.ajax({
                url: equipmentPartUrls.photos,
                dataType: 'json',
                type: 'GET',
                data: { id: id },
                success: function (response) {
                    $('#ideal-large-modal .modal-title').html('<i class="fas fa-images"></i> ' + '<?= Yii::t("app", "Manage Photos") ?>');
                    $('#ideal-large-modal .modal-body').html(response.content);
                    $('#ideal-large-modal').modal('show');
                },
                error: function (xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                    toastr.error('<?= Yii::t("app", "Error loading photos") ?>');
                }
            });
        });

        // Очистка содержимого при закрытии
        $('#ideal-large-modal').on('hidden.bs.modal', function () {
            $('#ideal-large-modal .modal-body').html('');
        });
    }

    function initializePartWriteOff() {
        $(document).off('click.part-write-off').on('click.part-write-off', '.part-write-off', function () {
            var id = $(this).data('id');
            $.ajax({
                url: equipmentPartUrls.writeOff,
                type: 'GET',
                data: { id: id },
                success: function (response) {
                    $('#ideal-mini-modal .modal-title').html('<?= Yii::t("app", "write_off") ?>');
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass('part-write-off-save');
                },
                error: function (xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.part-write-off-save').on('click.part-write-off-save', '.part-write-off-save', function () {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                clearValidationErrors();
                var formData = new FormData($('#part-write-off-form')[0]);

                $.ajax({
                    url: equipmentPartUrls.writeOff,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#part-grid-pjax',
                                complete: function () {
                                    initializeDataTable();
                                }
                            });
                        } else {
                            button.prop('disabled', false);
                            if (response.errors) {
                                displayValidationErrors(response.errors);
                            }
                        }
                    },
                    error: function (xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });
    }
})(jQuery);