<?php

namespace app\modules\backend\controllers;

use app\common\models\CashboxDetail;
use Yii;
use yii\web\Response;
use app\common\models\Tracking;
use app\common\models\Client;
use app\common\models\ClientBalance;
use app\modules\backend\models\Region;
use app\common\models\Sales;
use app\common\models\ClientPayments;
use app\common\models\ClientBalanceHistory;
use app\common\models\Cashbox;
use app\common\models\Product;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use app\common\models\SalesBonus;
use app\common\models\ClientContract;
use app\common\models\CurrencyCourse;

class ClientController extends BaseController
{
    public function actionIndex()
    {
        $query = Client::find()
            ->select([
                'client.*',
                'region.name as region_name',
                'COALESCE(client_balance.amount, 0) as balance'
            ])
            ->leftJoin('region', 'client.region_id = region.id')
            ->leftJoin('client_balance', 'client.id = client_balance.client_id')
            ->orderBy(['client.full_name' => SORT_ASC]);

        $result = $query->asArray()->all();


        return $this->render('index', [
            'result' => $result
        ]);
    }

    public function actionCreate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $model = new Client();
            $model->scenario = Client::SCENARIO_CREATE;
            if ($model->load(Yii::$app->request->post()) && $model->validate()) {

                if (!empty($model->phone_number)) {
                    $model->phone_number = str_replace(' ', '', $model->phone_number);
                    if (!str_starts_with($model->phone_number, '+998')) {
                        $model->phone_number = '+998' . $model->phone_number;
                    }
                }

                if (!empty($model->phone_number_2)) {
                    $model->phone_number_2 = str_replace(' ', '', $model->phone_number_2);
                    if (!str_starts_with($model->phone_number_2, '+998')) {
                        $model->phone_number_2 = '+998' . $model->phone_number_2;
                    }
                }
                $model->created_at = date('Y-m-d H:i:s');

                // Начинаем транзакцию
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    if(!$model->save(false)) {
                        throw new \Exception('Ошибка при сохранении клиента');
                    }

                    // Обрабатываем чекбокс возможности возврата
                    if (!empty($model->can_return)) {
                        $model->setCanReturn(true);
                    }

                    $transaction->commit();

                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'record_successfully_created')
                    ];
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'errors' => ['general' => [$e->getMessage()]]
                    ];
                }
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $model = new Client();
            $regions = Region::find()->where(['deleted_at' => null])->all();
            return [
                'status' => 'success',
                'content' => $this->renderPartial('create', [
                    'model' => $model,
                    'regions' => $regions
                ])
            ];
        }

    }


    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $id = Yii::$app->request->post('Client')['id'];
            $model = Client::findOne($id);
            $model->scenario = Client::SCENARIO_CREATE;

            if ($model->load(Yii::$app->request->post()) && $model->validate()) {

                if (!empty($model->phone_number)) {
                    $model->phone_number = str_replace(' ', '', $model->phone_number);
                    if (!str_starts_with($model->phone_number, '+998')) {
                        $model->phone_number = '+998' . $model->phone_number;
                    }
                }

                if (!empty($model->phone_number_2)) {
                    $model->phone_number_2 = str_replace(' ', '', $model->phone_number_2);
                    if (!str_starts_with($model->phone_number_2, '+998')) {
                        $model->phone_number_2 = '+998' . $model->phone_number_2;
                    }
                }
                $model->created_at = date('Y-m-d H:i:s');

                // Начинаем транзакцию
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    if(!$model->save(false)) {
                        throw new \Exception('Ошибка при сохранении клиента');
                    }

                    // Обрабатываем чекбокс возможности возврата
                    if (!empty($model->can_return)) {
                        $model->setCanReturn(true);
                    } else {
                        $model->setCanReturn(false);
                    }

                    $transaction->commit();

                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'record_successfully_updated')
                    ];
                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'errors' => ['general' => [$e->getMessage()]]
                    ];
                }
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = Client::findOne($id);
            $regions = Region::find()->where(['deleted_at' => null])->all();

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'record_not_found')
                ];
            }

            if (!empty($model->phone_number) && str_starts_with($model->phone_number, '+998')) {
                $model->phone_number = substr($model->phone_number, 4);
            }

            if (!empty($model->phone_number_2) && str_starts_with($model->phone_number_2, '+998')) {
                $model->phone_number_2 = substr($model->phone_number_2, 4);
            }

            // Загружаем информацию о возможности возврата
            $model->can_return = $model->getCanReturn();

            return [
                'status' => 'success',
                'content' => $this->renderPartial('update', [
                    'model' => $model,
                    'regions' => $regions
                ])
            ];
        }
    }

    public function actionPay()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $model = new ClientPayments();
            $model->add_user_id = Yii::$app->user->id;
            $cashboxId = Yii::$app->request->post('ClientPayments')['cashbox_id'] ?? null;
            
            if ($model->load(Yii::$app->request->post()) && $model->validate()) {
                if (!$cashboxId) {
                    return [
                        'status' => 'error',
                        'message' => 'Выберите кассу'
                    ];
                }
                
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    // Получаем выбранную кассу
                    $cashbox = Cashbox::findOne(['id' => $cashboxId, 'deleted_at' => null]);
                    if (!$cashbox) {
                        throw new \Exception('Касса не найдена');
                    }
                    
                    $model->created_at = date('Y-m-d H:i:s');
                    $model->cashbox_id = $cashboxId;
                    if (!$model->save()) {
                        throw new \Exception('Тўловни сақлашда хатолик: ' . json_encode($model->getErrors(), JSON_UNESCAPED_UNICODE));
                    }

                    $clientBalance = ClientBalance::findOne(['client_id' => $model->client_id]);
                    if (!$clientBalance) {
                        $clientBalance = new ClientBalance();
                        $clientBalance->client_id = $model->client_id;
                        $clientBalance->amount = 0;
                    }

                    $oldAmount = $clientBalance->amount;
                    $clientBalance->amount += $model->summa;

                    if (!$clientBalance->save()) {
                        throw new \Exception('Балансни янгилашда хатолик!');
                    }

                    $balanceHistory = new ClientBalanceHistory();
                    $balanceHistory->client_id = $model->client_id;
                    $balanceHistory->amount = $clientBalance->amount;
                    $balanceHistory->old_amount = $oldAmount;
                    $balanceHistory->type = $model->type;

                    if (!$balanceHistory->save()) {
                        throw new \Exception('Баланс тарихини сақлашда хатолик!');
                    }

                    // Определяем сумму для зачисления в кассу (с конвертацией если нужно)
                    $amountForCashbox = $model->summa;
                    $clientCurrencyId = 2; // Платежи клиентов в сомах
                    $cashboxCurrencyId = $cashbox->currency_id;
                    
                    if ($clientCurrencyId != $cashboxCurrencyId) {
                        // Нужна конвертация валют
                        try {
                            $conversionInfo = CurrencyCourse::convertCurrency($model->summa, $clientCurrencyId, $cashboxCurrencyId);
                            $amountForCashbox = $conversionInfo['amount'];
                        } catch (\Exception $e) {
                            throw new \Exception('Ошибка конвертации валют: ' . $e->getMessage());
                        }
                    }

                    $cashboxDetail = new CashboxDetail();
                    $cashboxDetail->cashbox_id = $cashbox->id;
                    $cashboxDetail->add_user_id = Yii::$app->user->id;
                    $cashboxDetail->type = CashboxDetail::TYPE_IN;
                    $cashboxDetail->amount = $amountForCashbox; // Сумма с учетом конвертации
                    $cashboxDetail->created_at = date('Y-m-d H:i:s');

                    if (!$cashboxDetail->save()) {
                        throw new \Exception('Касса деталини сақлашда хатолик: ' . json_encode($cashboxDetail->getErrors(), JSON_UNESCAPED_UNICODE));
                    }

                    $cashbox->balance += $amountForCashbox;
                    if (!$cashbox->save(false)) { // save(false) чтобы избежать валидации
                        throw new \Exception('Касса балансини янгилашда хатолик!');
                    }

                    $tracking = new Tracking();
                    $tracking->progress_type = Tracking::PAY_FOR_CLIENT;
                    $tracking->process_id = $model->id;
                    $tracking->status = Tracking::STATUS_NOT_ACCEPTED;
                    $tracking->accepted_at = null;
                    $tracking->created_at = date('Y-m-d H:i:s');

                    if (!$tracking->save()) {
                        throw new \Exception('Кузатувни сақлашда хатолик: ' . json_encode($tracking->getErrors(), JSON_UNESCAPED_UNICODE));
                    }

                    $transaction->commit();
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'payment_created_successfully')
                    ];

                } catch (\Exception $e) {
                    $transaction->rollBack();
                    Yii::error($e->getMessage());
                    return [
                        'status' => 'error',
                        'message' => $e->getMessage()
                    ];
                }
            }

            return [
                'status' => 'error',
                'errors' => $model->getErrors()
            ];

        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = new ClientPayments();
            $model->client_id = $id;

            return [
                'status' => 'success',
                'content' => $this->renderPartial('_payment_form', [
                    'model' => $model,
                ])
            ];
        }

        return [
            'status' => 'error',
            'message' => 'Invalid request'
        ];
    }

    public function actionUpdatePayment()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $id = $data['ClientPayments']['id'];

            // Проверяем, что платеж существует
            $model = ClientPayments::findOne($id);
            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Record not found')
                ];
            }

            // Проверяем, что tracking не подтвержден
            $tracking = Tracking::find()
                ->where(['process_id' => $id])
                ->andWhere(['progress_type' => Tracking::PAY_FOR_CLIENT])
                ->andWhere(['is', 'accepted_at', null])
                ->andWhere(['is', 'deleted_at', null])
                ->one();

            if (!$tracking) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Cannot update confirmed payment')
                ];
            }

            // Сохраняем старые значения для отката изменений
            $oldSumma = $model->summa;
            $oldType = $model->type;

            // Загружаем новые данные
            $model->load($data);

            if (!$model->validate()) {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors()
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                // 1. Обновляем баланс клиента
                $clientBalance = ClientBalance::findOne(['client_id' => $model->client_id]);
                if (!$clientBalance) {
                    throw new \Exception(Yii::t('app', 'Client balance not found'));
                }

                $oldAmount = $clientBalance->amount;
                // Вычитаем старую сумму и добавляем новую
                $clientBalance->amount = $clientBalance->amount - $oldSumma + $model->summa;

                if (!$clientBalance->save()) {
                    throw new \Exception(Yii::t('app', 'Error updating client balance'));
                }

                // 2. Создаем запись в истории баланса
                $balanceHistory = new ClientBalanceHistory();
                $balanceHistory->client_id = $model->client_id;
                $balanceHistory->amount = $clientBalance->amount;
                $balanceHistory->old_amount = $oldAmount;
                $balanceHistory->type = $model->type;

                if (!$balanceHistory->save()) {
                    throw new \Exception(Yii::t('app', 'Error saving balance history'));
                }

                // 3. Обновляем кассу (старый тип платежа)
                $oldCashbox = Cashbox::findByPaymentTypeAndCurrency($oldType, 2);

                if (!$oldCashbox) {
                    throw new \Exception(Yii::t('app', 'Old cashbox not found'));
                }

                // Вычитаем старую сумму из старой кассы
                $oldCashbox->balance -= $oldSumma;
                if (!$oldCashbox->save()) {
                    throw new \Exception(Yii::t('app', 'Error updating old cashbox balance'));
                }

                // Создаем запись о выходе средств из старой кассы
                $oldCashboxDetail = new CashboxDetail();
                $oldCashboxDetail->cashbox_id = $oldCashbox->id;
                $oldCashboxDetail->add_user_id = Yii::$app->user->id;
                $oldCashboxDetail->type = CashboxDetail::TYPE_OUT;
                $oldCashboxDetail->amount = $oldSumma;
                $oldCashboxDetail->created_at = date('Y-m-d H:i:s');

                if (!$oldCashboxDetail->save()) {
                    throw new \Exception(Yii::t('app', 'Error saving old cashbox detail'));
                }

                // 4. Обновляем кассу (новый тип платежа)
                $newCashbox = Cashbox::findByPaymentTypeAndCurrency($model->type, 2);

                if (!$newCashbox) {
                    throw new \Exception(Yii::t('app', 'New cashbox not found'));
                }

                // Добавляем новую сумму в новую кассу
                $newCashbox->balance += $model->summa;
                if (!$newCashbox->save()) {
                    throw new \Exception(Yii::t('app', 'Error updating new cashbox balance'));
                }

                // Создаем запись о входе средств в новую кассу
                $newCashboxDetail = new CashboxDetail();
                $newCashboxDetail->cashbox_id = $newCashbox->id;
                $newCashboxDetail->add_user_id = Yii::$app->user->id;
                $newCashboxDetail->type = CashboxDetail::TYPE_IN;
                $newCashboxDetail->amount = $model->summa;
                $newCashboxDetail->created_at = date('Y-m-d H:i:s');

                if (!$newCashboxDetail->save()) {
                    throw new \Exception(Yii::t('app', 'Error saving new cashbox detail'));
                }

                // 5. Обновляем платеж
                $model->created_at = date('Y-m-d H:i:s');
                if (!$model->save()) {
                    throw new \Exception(Yii::t('app', 'Error updating payment'));
                }

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'payment_updated_successfully')
                ];

            } catch (\Exception $e) {
                $transaction->rollBack();
                Yii::error($e->getMessage());
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = ClientPayments::findOne($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Record not found')
                ];
            }

            // Проверяем, что tracking не подтвержден
            $tracking = Tracking::find()
                ->where(['process_id' => $id])
                ->andWhere(['progress_type' => Tracking::PAY_FOR_CLIENT])
                ->andWhere(['is', 'accepted_at', null])
                ->andWhere(['is', 'deleted_at', null])
                ->one();

            if (!$tracking) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Cannot update confirmed payment')
                ];
            }

            return [
                'status' => 'success',
                'content' => $this->renderPartial('_payment_update_form', [
                    'model' => $model,
                ])
            ];
        }

        return [
            'status' => 'error',
            'message' => 'Invalid request'
        ];
    }

    public function actionView()
    {
        if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $dateFrom = Yii::$app->request->get('date_from', date('Y-m-d'));
            $dateTo = Yii::$app->request->get('date_to', date('Y-m-d'));

            // Получаем текущую страницу для продаж и платежей
            $salesPage = (int)Yii::$app->request->get('sales_page', 1);
            $paymentsPage = (int)Yii::$app->request->get('payments_page', 1);
            $pageSize = 20;

            $model = Client::find()
                ->select([
                    'client.*',
                    'region.name as region_name',
                    'COALESCE(client_balance.amount, 0) as balance'
                ])
                ->leftJoin('region', 'client.region_id = region.id')
                ->leftJoin('client_balance', 'client.id = client_balance.client_id')
                ->where(['client.id' => $id])
                ->asArray()
                ->one();

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'record_not_found')
                ];
            }

            // Запрос для общего количества продаж
            $totalSales = Sales::find()
                ->where(['client_id' => $id])
                ->andWhere(['>=', 'created_at', $dateFrom . ' 00:00:00'])
                ->andWhere(['<=', 'created_at', $dateTo . ' 23:59:59'])
                ->count();

            // Запрос продаж с пагинацией
            $sales = Sales::find()
                ->select([
                    'sales.id',
                    'sales.started_at as arrival_time',
                    'sales.completed_at as departure_time',
                    'sales.car_number',
                    'sales.driver',
                    'users.full_name as confirm_user'
                ])
                ->leftJoin('users', 'sales.confirm_user_id = users.id')
                ->where(['sales.client_id' => $id])
                ->andWhere(['>=', 'sales.created_at', $dateFrom . ' 00:00:00'])
                ->andWhere(['<=', 'sales.created_at', $dateTo . ' 23:59:59'])
                ->orderBy(['sales.created_at' => SORT_DESC])
                ->offset(($salesPage - 1) * $pageSize)
                ->limit($pageSize)
                ->asArray()
                ->all();

            $products = Product::find()
                ->select(['id', 'name'])
                ->where(['deleted_at' => null])
                ->orderBy(['name' => SORT_ASC])
                ->asArray()
                ->all();

            // Модифицируем SQL запрос для получения деталей только для текущей страницы продаж
            $saleIds = array_column($sales, 'id');
            $sql = "SELECT
                        sales.id,
                        COALESCE(sales_detail.product_id, 0) AS product_id,
                        COALESCE(sales_detail.quantity, 0) AS quantity,
                        COALESCE(sales_detail.special_price, 0) AS factory_price,
                        sales_detail.special_price * sales_detail.quantity AS total
                    FROM
                        sales
                    LEFT JOIN
                        sales_detail ON sales.id = sales_detail.sale_id
                    WHERE
                        sales.client_id = :client_id
                        AND sales.id IN (" . implode(',', $saleIds ?: [0]) . ")
                        AND sales.deleted_at IS NULL
                        AND sales_detail.deleted_at IS NULL;";

            $salesDetails = Yii::$app->db->createCommand($sql)
                ->bindValue(':client_id', $id)
                ->queryAll();


            $groupedSales = [];
            foreach ($sales as $sale) {
                $saleId = $sale['id'];
                $groupedSales[$saleId] = [
                    'id' => $saleId,
                    'arrival_time' => $sale['arrival_time'],
                    'departure_time' => $sale['departure_time'],
                    'car_number' => $sale['car_number'],
                    'driver' => $sale['driver'],
                    'accepted_user' => $sale['confirm_user'],
                    'products' => []
                ];

                foreach ($products as $product) {
                    $groupedSales[$saleId]['products'][$product['id']] = [
                        'quantity' => 0,
                        'price' => 0,
                        'total' => 0
                    ];
                }
            }

            foreach ($salesDetails as $detail) {
                if ($detail['product_id'] && isset($groupedSales[$detail['id']]['products'][$detail['product_id']])) {
                    $groupedSales[$detail['id']]['products'][$detail['product_id']] = [
                        'quantity' => $detail['quantity'],
                        'price' => $detail['factory_price'],
                        'total' => $detail['total']
                    ];
                }
            }

            // Получаем общее количество платежей
            $totalPayments = ClientPayments::find()
                ->where(['client_id' => $id])
                ->andWhere(['deleted_at' => null])
                ->count();

            // Запрос платежей с пагинацией
            $payments = ClientPayments::find()
                ->select([
                    'client_payments.*',
                    'users.username as added_by'
                ])
                ->leftJoin('users', 'client_payments.add_user_id = users.id')
                ->where(['client_payments.client_id' => $id])
                ->andWhere(['client_payments.deleted_at' => null])
                ->orderBy(['client_payments.created_at' => SORT_DESC])
                ->offset(($paymentsPage - 1) * $pageSize)
                ->limit($pageSize)
                ->asArray()
                ->all();

            $balanceHistory = ClientBalanceHistory::find()
                ->where(['client_id' => $id])
                ->orderBy(['created_at' => SORT_DESC])
                ->asArray()
                ->all();


            $contracts = ClientContract::find()
                ->where(['client_id' => $id])
                ->orderBy(['created_at' => SORT_DESC])
                ->asArray()
                ->all();


            return $this->render('view', [
                'model' => $model,
                'products' => $products,
                'sales' => $groupedSales,
                'payments' => $payments,
                'balanceHistory' => $balanceHistory,
                'dateFrom' => $dateFrom,
                'dateTo' => $dateTo,
                'totalSales' => $totalSales,
                'totalPayments' => $totalPayments,
                'pageSize' => $pageSize,
                'salesPage' => $salesPage,
                'paymentsPage' => $paymentsPage,
                'contracts' => $contracts
            ]);
        }
    }

    public function actionContract()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $model = new ClientContract();
            if ($model->load(Yii::$app->request->post(), '')) {

                if(!$model->validate()) {
                    return [
                        'status' => 'error',
                        'errors' => $model->getErrors()
                    ];
                }

                $oldContract = ClientContract::findOne(['client_id' => $model->client_id, 'deleted_at' => null]);
                if ($oldContract) {
                    $oldContract->deleted_at = date('Y-m-d H:i:s');
                    $oldContract->end_date = date('Y-m-d');
                    $oldContract->save(false);
                }


                $model->created_at = date('Y-m-d H:i:s');
                if(!$model->save(false)) {
                    return [
                        'status' => 'error',
                        'errors' => $model->getErrors()
                    ];
                }


                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'record_successfully_created')
                ];
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = new ClientContract();
            $model->client_id = $id;

            return [
                'status' => 'success',
                'content' => $this->renderPartial('_contract_form', [
                    'model' => $model,
                ])
            ];
        }
    }















    public function actionDelete()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $model = Client::findOne($data['Client']['id']);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'record_not_found')
                ];
            }

            // Устанавливаем дату удаления (мягкое удаление)
            $model->deleted_at = date('Y-m-d H:i:s');

            if ($model->save(false)) {
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'record_successfully_deleted')
                ];
            } else {
                return [
                    'status' => 'error',
                    'errors' => $model->getErrors()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = Client::findOne($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'record_not_found')
                ];
            }

            return [
                'status' => 'success',
                'content' => $this->renderPartial('delete', [
                    'model' => $model
                ])
            ];
        }
    }

    public function actionExcelClientData()
    {
        $request = Yii::$app->request;
        $clientId = $request->post('client_id');
        $startDate = $request->post('start_date');
        $endDate = $request->post('end_date');

        // Получаем продажи клиента за выбранный период
        $sales = Sales::find()
            ->where(['client_id' => $clientId])
            ->andWhere(['>=', 'DATE(created_at)', $startDate])
            ->andWhere(['<=', 'DATE(created_at)', $endDate])
            ->orderBy(['id' => SORT_ASC])
            ->all();

        // Подготавливаем данные для отчета
        $reportData = [
            'bonus' => [
                'items' => []
            ]
        ];

        // Получаем бонусы для каждой продажи
        foreach ($sales as $sale) {
            $bonuses = SalesBonus::find()
                ->alias('sb')
                ->select(['sb.*', 'p.name as product_name'])
                ->leftJoin('product p', 'p.id = sb.product_id')
                ->where(['sb.sale_id' => $sale->id])
                ->andWhere(['IS', 'sb.deleted_at', null])
                ->asArray()
                ->all();

            if (!empty($bonuses)) {
                $reportData['bonus']['items'][$sale->id] = array_map(function($bonus) {
                    return [
                        'product_name' => $bonus['product_name'],
                        'total_quantity' => $bonus['quantity']
                    ];
                }, $bonuses);
            }
        }

        // Проверяем, что запрос POST
        if (Yii::$app->request->isPost) {
            // Получаем параметры
            $startDate = Yii::$app->request->post('start_date');
            $endDate = Yii::$app->request->post('end_date');
            $clientId = Yii::$app->request->post('client_id');

            // Проверяем наличие обязательных параметров
            if (!$startDate || !$endDate || !$clientId) {
                throw new \yii\web\BadRequestHttpException('Отсутствуют обязательные параметры');
            }

            // Получаем информацию о клиенте
            $client = Client::find()
                ->select(['full_name', 'account_number'])
                ->where(['id' => $clientId])
                ->asArray()
                ->one();

            if (!$client) {
                throw new \yii\web\NotFoundHttpException('Клиент не найден');
            }

            // Получаем продукты
            $products = Product::find()
                ->select(['id', 'name'])
                ->where(['deleted_at' => null])
                ->orderBy(['name' => SORT_ASC])
                ->asArray()
                ->all();

            // Получаем продажи за указанный период
            $sales = Sales::find()
                ->select([
                    'sales.id',
                    'sales.started_at as arrival_time',
                    'sales.completed_at as departure_time',
                    'sales.car_number',
                    'sales.created_at',
                    'users.full_name as accepted_user'
                ])
                ->leftJoin('users', 'sales.confirm_user_id = users.id')
                ->where(['sales.client_id' => $clientId])
                ->andWhere(['>=', 'DATE(sales.created_at)', $startDate])
                ->andWhere(['<=', 'DATE(sales.created_at)', $endDate])
                ->andWhere(['sales.deleted_at' => null])
                ->orderBy(['sales.created_at' => SORT_DESC])
                ->asArray()
                ->all();

            // Если нет продаж, возвращаем сообщение
            if (empty($sales)) {
                Yii::$app->session->setFlash('error', 'Нет данных о продажах за указанный период');
                return $this->redirect(['view', 'id' => $clientId]);
            }

            // Получаем идентификаторы продаж
            $saleIds = array_column($sales, 'id');

            // Получаем детали продаж
            $sql = "SELECT
                        sales.id as sale_id,
                        sales_detail.product_id,
                        product.name as product_name,
                        product.size as product_size,
                        COALESCE(sales_detail.quantity, 0) AS quantity,
                        COALESCE(sales_detail.factory_price, 0) AS price,
                        COALESCE(sales_detail.total_price, 0) AS total
                    FROM
                        sales
                    LEFT JOIN
                        sales_detail ON sales.id = sales_detail.sale_id
                    LEFT JOIN
                        product ON sales_detail.product_id = product.id
                    WHERE
                        sales.id IN (" . implode(',', $saleIds) . ")
                        AND sales.deleted_at IS NULL
                        AND sales_detail.deleted_at IS NULL;";

            $salesDetails = Yii::$app->db->createCommand($sql)->queryAll();

            // Группируем детали продаж по ID продажи
            $groupedSales = [];
            foreach ($sales as $sale) {
                $saleId = $sale['id'];
                $groupedSales[$saleId] = [
                    'id' => $saleId,
                    'arrival_time' => $sale['arrival_time'],
                    'departure_time' => $sale['departure_time'],
                    'car_number' => $sale['car_number'],
                    'accepted_user' => $sale['accepted_user'],
                    'created_at' => $sale['created_at'],
                    'products' => []
                ];
            }

            // Заполняем детали продаж
            foreach ($salesDetails as $detail) {
                $saleId = $detail['sale_id'];
                if (isset($groupedSales[$saleId])) {
                    $blocks = $detail['product_size'] > 0 ? $detail['quantity'] / $detail['product_size'] : 0;
                    $groupedSales[$saleId]['products'][] = [
                        'product_id' => $detail['product_id'],
                        'product_name' => $detail['product_name'],
                        'quantity' => $detail['quantity'],
                        'blocks' => $blocks,
                        'price' => $detail['price'],
                        'total' => $detail['total']
                    ];
                }
            }

            // Создаем Excel файл
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();

            // Устанавливаем заголовок
            $sheet->setCellValue('A1', 'Отчет о продажах клиента: ' . $client['full_name']);
            $sheet->setCellValue('A2', 'Лицевой счет: ' . $client['account_number']);
            $sheet->setCellValue('A3', 'Период: с ' . date('d.m.Y', strtotime($startDate)) . ' по ' . date('d.m.Y', strtotime($endDate)));

            // Стилизуем заголовок
            $sheet->mergeCells('A1:H1');
            $sheet->mergeCells('A2:H2');
            $sheet->mergeCells('A3:H3');

            $sheet->getStyle('A1:A3')->getFont()->setBold(true);
            $sheet->getStyle('A1:A3')->getFont()->setSize(14);
            $sheet->getStyle('A1:H3')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

            // Заголовки таблицы
            $sheet->setCellValue('A5', 'ID продажи');
            $sheet->setCellValue('B5', 'Дата продажи');
            $sheet->setCellValue('C5', 'Продукт');
            $sheet->setCellValue('D5', 'Количество (шт)');
            $sheet->setCellValue('E5', 'Блоки');
            $sheet->setCellValue('F5', 'Сумма');
            $sheet->setCellValue('G5', 'Бонусные продукты');
            $sheet->setCellValue('H5', 'Количество бонусов (шт)');

            // Стилизуем заголовки таблицы
            $sheet->getStyle('A5:H5')->getFont()->setBold(true);
            $sheet->getStyle('A5:H5')->getFill()->setFillType(Fill::FILL_SOLID);
            $sheet->getStyle('A5:H5')->getFill()->getStartColor()->setRGB('DDDDDD');
            $sheet->getStyle('A5:H5')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

            // Устанавливаем ширину столбцов
            $sheet->getColumnDimension('A')->setWidth(10);
            $sheet->getColumnDimension('B')->setWidth(15);
            $sheet->getColumnDimension('C')->setWidth(20);
            $sheet->getColumnDimension('D')->setWidth(15);
            $sheet->getColumnDimension('E')->setWidth(10);
            $sheet->getColumnDimension('F')->setWidth(15);
            $sheet->getColumnDimension('G')->setWidth(20);
            $sheet->getColumnDimension('H')->setWidth(20);

            // Заполняем данные
            $row = 6;
            $totalQuantity = 0;
            $totalAmount = 0;

            foreach ($groupedSales as $saleId => $sale) {
                $firstRow = $row;
                $productCount = count($sale['products']);
                $rowspan = max(1, $productCount);

                // Для каждого продукта в продаже
                foreach ($sale['products'] as $index => $product) {
                    // ID продажи только в первой строке продажи
                    if ($index === 0) {
                        $sheet->setCellValue('A' . $row, $saleId);
                    }

                    // Дата продажи только в первой строке продажи
                    if ($index === 0) {
                        $sheet->setCellValue('B' . $row, date('d.m.Y', strtotime($sale['created_at'])));
                    }

                    // Информация о продукте
                    $sheet->setCellValue('C' . $row, $product['product_name']);
                    $sheet->setCellValue('D' . $row, $product['quantity']);

                    // Блоки (рассчитываем из quantity и size)
                    $sheet->setCellValue('E' . $row, number_format($product['blocks'], 2));

                    // Сумма
                    $sheet->setCellValue('F' . $row, $product['total']);
                    $sheet->getStyle('F' . $row)->getNumberFormat()->setFormatCode('#,##0');

                    // Бонусы (предполагается, что бонусы добавляются в последнюю строку продажи)
                    if ($index === 0 && isset($reportData['bonus']['items'][$saleId])) {
                        $bonusItems = $reportData['bonus']['items'][$saleId];
                        $bonusText = '';
                        $bonusQuantity = '';

                        foreach ($bonusItems as $bonusItem) {
                            if (!empty($bonusText)) {
                                $bonusText .= "\n";
                                $bonusQuantity .= "\n";
                            }
                            $bonusText .= $bonusItem['product_name'];
                            $bonusQuantity .= $bonusItem['total_quantity'];
                        }

                        $sheet->setCellValue('G' . $row, $bonusText);
                        $sheet->setCellValue('H' . $row, $bonusQuantity);

                        // Устанавливаем перенос текста для бонусов
                        $sheet->getStyle('G' . $row)->getAlignment()->setWrapText(true);
                        $sheet->getStyle('H' . $row)->getAlignment()->setWrapText(true);
                    }

                    // Обновляем итоги
                    $totalQuantity += $product['quantity'];
                    $totalAmount += $product['total'];

                    $row++;
                }

                // Если нет продуктов, все равно добавляем строку
                if ($productCount === 0) {
                    $sheet->setCellValue('A' . $row, $saleId);
                    $sheet->setCellValue('B' . $row, date('d.m.Y', strtotime($sale['created_at'])));
                    $row++;
                }

                // Добавляем объединение ячеек для ID и даты продажи
                if ($rowspan > 1) {
                    $sheet->mergeCells('A' . $firstRow . ':A' . ($firstRow + $rowspan - 1));
                    $sheet->mergeCells('B' . $firstRow . ':B' . ($firstRow + $rowspan - 1));

                    // Если есть бонусы, объединяем и для них
                    if (isset($reportData['bonus']['items'][$saleId])) {
                        $sheet->mergeCells('G' . $firstRow . ':G' . ($firstRow + $rowspan - 1));
                        $sheet->mergeCells('H' . $firstRow . ':H' . ($firstRow + $rowspan - 1));
                    }
                }

                // Центрируем ID и дату по вертикали
                $sheet->getStyle('A' . $firstRow . ':B' . ($firstRow + $rowspan - 1))->getAlignment()
                    ->setVertical(Alignment::VERTICAL_CENTER);

                // Если есть бонусы, центрируем их тоже
                if (isset($reportData['bonus']['items'][$saleId])) {
                    $sheet->getStyle('G' . $firstRow . ':H' . ($firstRow + $rowspan - 1))->getAlignment()
                        ->setVertical(Alignment::VERTICAL_CENTER);
                }
            }

            // Добавляем итоговую строку
            $sheet->setCellValue('A' . $row, 'ИТОГО:');
            $sheet->mergeCells('A' . $row . ':C' . $row);
            $sheet->setCellValue('D' . $row, $totalQuantity);
            $sheet->setCellValue('F' . $row, $totalAmount);
            $sheet->getStyle('F' . $row)->getNumberFormat()->setFormatCode('#,##0');

            // Стилизуем итоговую строку
            $sheet->getStyle('A' . $row . ':H' . $row)->getFont()->setBold(true);
            $sheet->getStyle('A' . $row . ':H' . $row)->getFill()->setFillType(Fill::FILL_SOLID);
            $sheet->getStyle('A' . $row . ':H' . $row)->getFill()->getStartColor()->setRGB('EEEEEE');

            // Центрируем итоговую строку
            $sheet->getStyle('A' . $row . ':H' . $row)->getAlignment()
                ->setHorizontal(Alignment::HORIZONTAL_CENTER);

            // Добавляем границы для всей таблицы
            $sheet->getStyle('A5:H' . $row)->getBorders()->getAllBorders()
                ->setBorderStyle(Border::BORDER_THIN);

            // Устанавливаем формат файла
            $writer = new Xlsx($spreadsheet);

            // Формируем имя файла
            $filename = 'Отчет_клиента_' . $client['full_name'] . '_' . date('d-m-Y') . '.xlsx';

            // Устанавливаем заголовки для скачивания
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            header('Content-Disposition: attachment;filename="' . $filename . '"');
            header('Cache-Control: max-age=0');

            // Выводим содержимое файла в поток
            $writer->save('php://output');
            exit;
        }

        // Если не POST запрос, перенаправляем на главную страницу
        return $this->redirect(['index']);
    }
}
