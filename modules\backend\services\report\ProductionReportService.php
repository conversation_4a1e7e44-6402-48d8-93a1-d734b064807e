<?php

namespace app\modules\backend\services\report;

use yii\db\Query;
use yii\helpers\ArrayHelper;

/**
 * Сервис для формирования отчетов по производству
 */
class ProductionReportService
{
    /**
     * Получить отчет по выпущенным продуктам за указанную дату (сегодня по умолчанию)
     * 
     * @param string $date
     * @param int|null $productId
     * @return array
     */
    public function getTodayProductionReport($date = null, $productId = null)
    {
        if ($date === null) {
            $date = date('Y-m-d');
        }

        $producedProducts = $this->getProducedProductsData($date, $productId);
        
        return [
            'produced_products' => $producedProducts,
            'summary' => [
                'totalProducts' => $producedProducts['total_products'] ?? 0,
                'totalQuantityProduced' => $producedProducts['total_quantity_produced'] ?? 0
            ]
        ];
    }
   
    public function getProductionReport($startDate = null, $endDate = null, $productId = null, $workerId = null)
    {
        $materialsAvailable = $this->getMaterialsAvailableData($productId);
        
        return [
            'materials_available' => $materialsAvailable,
            'summary' => [
                'totalMaterialTypes' => $materialsAvailable['total_materials'] ?? 0,
                'totalQuantityAvailable' => $materialsAvailable['total_quantity_available'] ?? 0
            ]
        ];
    }

    /**
     * Получить данные по выпущенным продуктам за указанную дату
     * 
     * @param string $date
     * @param int|null $productId
     * @return array
     */
    protected function getProducedProductsData($date, $productId = null)
    {
        $query = new Query();
        $query->select([
                'psh.id as history_id',
                'p.id as product_id',
                'p.name as product_name',
                'psh.quantity',
                'psh.created_at as enter_date',
                'u_added.full_name as added_by',
                'ps.accepted_at',
                'u_accepted.full_name as accepted_by',
                new \yii\db\Expression("CASE\n                    WHEN ps.accepted_at IS NOT NULL AND ps.accepted_user_id IS NOT NULL THEN 'Принят'\n                    ELSE 'Ожидает приемки'\n                END as status")
            ])
            ->from(['psh' => 'product_storage_history'])
            ->join('LEFT JOIN', ['ps' => 'product_storage'], 'ps.id = psh.product_storage_id')
            ->join('LEFT JOIN', ['p' => 'product'], 'p.id = psh.product_id')
            ->join('LEFT JOIN', ['u_added' => 'users'], 'u_added.id = psh.add_user_id')
            ->join('LEFT JOIN', ['u_accepted' => 'users'], 'u_accepted.id = ps.accepted_user_id')
            ->where([
                'psh.type' => 'income',
                'psh.deleted_at' => null
            ])
            ->andWhere(new \yii\db\Expression("(psh.created_at AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Tashkent')::date = :date", [':date' => $date]));

        if ($productId) {
            $query->andWhere(['psh.product_id' => $productId]);
        }

        $query->orderBy(['psh.created_at' => SORT_DESC]);

        $items = $query->all();

        // Форматируем
        $formattedItems = [];
        $totalQuantity = 0;
        foreach ($items as $item) {
            $qty = (float)$item['quantity'];
            $totalQuantity += $qty;
            $formattedItems[] = [
                'history_id' => $item['history_id'],
                'product_id' => $item['product_id'],
                'product_name' => $item['product_name'] ?: 'Неопределенный продукт',
                'quantity' => number_format($qty, 2, '.', ' '),
                'enter_date' => $item['enter_date'],
                'added_by' => $item['added_by'] ?: '-',
                'accepted_at' => $item['accepted_at'],
                'accepted_by' => $item['accepted_by'] ?: '-',
                'status' => $item['status']
            ];
        }

        return [
            'items' => $formattedItems,
            'total_products' => count($formattedItems),
            'total_quantity_produced' => $totalQuantity
        ];
    }

    /**
     * Получить данные по наличию материалов в производстве
     * 
     * @param int|null $productId
     * @return array
     */
    protected function getMaterialsAvailableData($productId = null)
    {
        $query = new Query();
        $query->select([
                'm.id as material_id',
                'm.name as material_name',
                'SUM(mp.quantity) as available_quantity',
                'm.unit_type as unit'
            ])
            ->from(['mp' => 'material_production'])
            ->leftJoin(['m' => 'material'], 'mp.material_id = m.id')
            ->andWhere(['IS', 'mp.deleted_at', null])
            ->andWhere(['IS', 'm.deleted_at', null]);
            
        if ($productId) {
            // Фильтр по продукту через ингредиенты
            $query->leftJoin(['pi' => 'product_ingredients'], 'pi.material_id = m.id')
                  ->andWhere(['pi.product_id' => $productId]);
        }
        
        // Группируем по материалу
        $query->groupBy(['m.id', 'm.name', 'm.unit_type'])
              ->orderBy(['available_quantity' => SORT_DESC, 'm.name' => SORT_ASC]);
        
        $items = $query->all();
        
        // Получаем данные о браке для каждого материала
        $defectData = $this->getDefectData();
        
        // Преобразуем результат для удобства отображения
        $formattedItems = [];
        foreach ($items as $item) {
            $materialId = $item['material_id'];
            $defectQuantity = isset($defectData[$materialId]) ? $defectData[$materialId] : 0;
            
            $formattedItems[] = [
                'material_id' => $materialId,
                'material_name' => $item['material_name'] ?: 'Неопределенный материал',
                'available_quantity' => number_format((float)$item['available_quantity'], 0, '.', ''),
                'defect_quantity' => $defectQuantity,
                'unit' => $this->getUnitName($item['unit']),
            ];
        }
        
        return [
            'items' => $formattedItems,
            'total_materials' => count($formattedItems),
            'total_quantity_available' => array_sum(array_column($formattedItems, 'available_quantity')),
            'total_defect' => array_sum(array_column($formattedItems, 'defect_quantity'))
        ];
    }

    /**
     * Получить данные о браке по материалам
     * 
     * @return array
     */
    protected function getDefectData()
    {
        $query = new Query();
        $query->select([
                'md.material_id',
                'SUM(md.quantity) as defect_quantity'
            ])
            ->from(['md' => 'material_defect'])
            ->where(['IS', 'md.deleted_at', null])
            ->groupBy(['md.material_id']);
        
        $items = $query->all();
        
        $defectData = [];
        foreach ($items as $item) {
            $defectData[$item['material_id']] = (int)$item['defect_quantity'];
        }
        
        return $defectData;
    }

    /**
     * Получить название единицы измерения
     * 
     * @param int $unitType
     * @return string
     */
    protected function getUnitName($unitType)
    {
        switch ($unitType) {
            case 1:
                return 'шт';
            case 2:
                return 'кг';
            case 3:
                return 'л';
            case 4:
                return 'м';
            default:
                return 'шт';
        }
    }
}