<?php

namespace app\modules\backend\services\report;

use yii\db\Query;
use yii\helpers\ArrayHelper;
use app\common\models\CashboxDetail;

/**
 * Сервис формирования отчёта по кассам (приход/расход по кассам)
 */
class CashboxReportService
{
    /**
     * Получить данные отчёта по кассам
     *
     * @param string|null $startDate Дата начала (Y-m-d)
     * @param string|null $endDate   Дата окончания (Y-m-d)
     * @param int|null    $cashboxId ID конкретной кассы (если нужен фильтр)
     *
     * @return array
     */
    public function getCashboxReport($startDate = null, $endDate = null, $cashboxId = null)
    {
        $query = (new Query())
            ->select([
                'cd.id',
                'cd.cashbox_id',
                'c.title   AS cashbox_name',
                'cur.name  AS currency',
                'cd.amount',
                'cd.type',
                'cd.created_at',
                'u.full_name AS user_name',
            ])
            ->from(['cd' => 'cashbox_detail'])
            ->leftJoin(['c'   => 'cashbox'], 'cd.cashbox_id = c.id')
            ->leftJoin(['cur' => 'currency'], 'c.currency_id = cur.id')
            ->leftJoin(['u'   => 'users'], 'cd.add_user_id = u.id')
            ->where(['IS', 'cd.deleted_at', null]);

        if ($cashboxId) {
            $query->andWhere(['cd.cashbox_id' => $cashboxId]);
        }
        if ($startDate) {
            $query->andWhere(['>=', 'cd.created_at', $startDate . ' 00:00:00']);
        }
        if ($endDate) {
            $query->andWhere(['<=', 'cd.created_at', $endDate . ' 23:59:59']);
        }

        $query->orderBy(['cd.created_at' => SORT_DESC]);

        $items = $query->all();

        // Считаем итоги по валютам
        $totals = [];
        foreach ($items as $item) {
            $currency = strtoupper($item['currency'] ?? 'UZS');
            if (!isset($totals[$currency])) {
                $totals[$currency] = [
                    'in'  => 0,
                    'out' => 0,
                    'net' => 0,
                ];
            }

            if ((int)$item['type'] === CashboxDetail::TYPE_IN) {
                $totals[$currency]['in']  += (float)$item['amount'];
                $totals[$currency]['net'] += (float)$item['amount'];
            } else {
                $totals[$currency]['out'] += (float)$item['amount'];
                $totals[$currency]['net'] -= (float)$item['amount'];
            }
        }

        // Итоги сводим в более плоскую структуру для удобства вывода
        $summary = [];
        foreach ($totals as $cur => $data) {
            $summary[] = array_merge(['currency' => $cur], $data);
        }

        return [
            'items'   => $items,
            'summary' => $summary,
        ];
    }
} 