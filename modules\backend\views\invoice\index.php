<?php

use yii\helpers\Html;
use app\common\models\Sales;
use yii\widgets\Pjax;
use yii\web\View;
use app\assets\DataTablesAsset;
use app\common\models\Tracking;
use app\assets\Select2Asset;

DataTablesAsset::register($this);
Select2Asset::register($this);

$this->title = Yii::t("app", "invoice_section");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
$all = Yii::t("app", "all");
$four = Yii::t("app", "view_invoice");

?>

<style>
    .modal-dialog {
        max-width: 900px !important;
    }

    /* Стили для полей с ошибками */
    .is-invalid {
        border-color: #dc3545 !important;
        background-color: #fff8f8 !important;
    }

    /* Стили для Select2 с ошибками */
    .is-invalid + .select2-container .select2-selection {
        border-color: #dc3545 !important;
        background-color: #fff8f8 !important;
    }

    /* Стили для сообщений об ошибках */
    .error-message {
        color: #dc3545;
        font-size: 0.8rem;
        margin-top: 0.25rem;
        display: block;
    }
</style>
<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>

        <div class="col-md-6 text-right">

            <?php if (Yii::$app->user->can('admin') || Yii::$app->user->can('product_keeper')): ?>
                <a href="#" class="btn btn-primary product-return" data-toggle="modal" data-target="#ideal-mini-modal">
                    <?= Yii::t("app", "product_return") ?>
                </a>
            <?php endif ?>


            <?php if (Yii::$app->user->can('admin') || Yii::$app->user->can('product_keeper')): ?>
                <a href="/backend/invoice/sales" class="btn btn-primary">
                    <?= Yii::t("app", "view_sales") ?>
                </a>
            <?php endif ?>

            <?php if (Yii::$app->user->can('admin') || Yii::$app->user->can('product_keeper')): ?>
                <a href="#" class="btn btn-primary invoice-create" data-toggle="modal" data-target="#ideal-mini-modal">
                    <?= Yii::t("app", "add_invoice") ?>
                </a>
            <?php endif ?>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'invoice-grid-pjax']); ?>
    <?php if($result): ?>
        <div>
            <table id="invoice-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <tr>
                        <th><?= Yii::t("app", "id") ?></th>
                        <th><?= Yii::t("app", "client_name") ?></th>
                        <th><?= Yii::t("app", "total_quantity") ?></th>
                        <th><?= Yii::t("app", "total_sum") ?></th>
                        <th><?= Yii::t("app", "status") ?></th>
                        <th><?= Yii::t("app", "car_number") ?></th>
                        <th><?= Yii::t("app", "driver") ?></th>
                        <th><?= Yii::t("app", "sell_user") ?></th>
                        <th><?= Yii::t("app", "confirm_user") ?></th>
                        <th><?= Yii::t("app", "invoice_created_at") ?></th>
                        <th><?= Yii::t("app", "actions") ?></th>
                    </tr>
                </thead>
                <tbody>
                <?php foreach ($result as $model): ?>
                    <tr>
                        <td><?= Html::encode($model['id']) ?></td>
                        <td><?= Html::encode($model['client_name']) ?></td>
                        <td><?= Html::encode($model['total_quantity']) ?></td>
                        <td><?= is_numeric($model['total_sum']) ? number_format(floatval($model['total_sum']), 0, '.', ',') : '0' ?></td>
                        <td>
                                <?= Sales::getStatusList($model['status']) ?>
                        </td>
                        <td><?= Html::encode($model['car_number']) ?></td>
                        <td><?= Html::encode($model['driver']) ?></td>
                        <td><?= Html::encode($model['sell_user']) ?></td>
                        <td><?= Html::encode($model['confirm_user']) ?></td>
                        <td><?= Html::encode(date('d.m.Y H:i', strtotime($model['created_at']))) ?></td>

                        <td>
                            <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                    <?php echo Yii::t("app", "detail"); ?>
                                </a>
                                <div class="dropdown-menu">
                                    <a href="#" class="dropdown-item invoice-view" data-toggle="modal" data-target="#ideal-large-modal-without-save" data-id="<?= Html::encode($model['id']) ?>">
                                        <?= Yii::t("app", "view") ?>
                                    </a>
                                    <a href="#" class="dropdown-item invoice-print-option" data-id="<?= Html::encode($model['id']) ?>" data-with-price="true">
                                        <?= Yii::t("app", "print_with_price") ?>
                                    </a>
                                    <a href="#" class="dropdown-item invoice-print-option" data-id="<?= Html::encode($model['id']) ?>" data-with-price="false">
                                        <?= Yii::t("app", "print_without_price") ?>
                                    </a>
                                    <?php
                                    $tracking = Tracking::find()
                                    ->where([
                                        'process_id' => $model['id'],
                                        'progress_type' => Tracking::TYPE_NEW_SALES,
                                        'status' => Tracking::STATUS_NOT_ACCEPTED,
                                        'deleted_at' => null
                                    ])
                                    ->andWhere(['not', ['accepted_at' => null]])
                                    ->one();

                                    $canEdit = false;
                                    $canDelete = false;

                                    // Сначала проверяем tracking
                                    if ($tracking) {
                                        $canEdit = true;
                                    } else {
                                        // Если нет tracking, проверяем статус
                                        if ($model['status'] === Sales::STATUS_NEW || $model['status'] === Sales::STATUS_IN_PROGRESS) {
                                            $canEdit = true;
                                            $canDelete = true;
                                        } elseif ($model['status'] === Sales::STATUS_CONFIRMED) {
                                            $confirmDate = strtotime($model['completed_at']);
                                            $currentDate = time();
                                            $daysDiff = floor(($currentDate - $confirmDate) / (60 * 60 * 24));

                                            if ($daysDiff <= 7) {
                                                $canEdit = true;
                                            }
                                            $canDelete = false;
                                        }
                                    }

                                    if ($canEdit && $model['deleted_at'] === null) : ?>
                                        <a href="#" class="dropdown-item invoice-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "edit") ?>
                                        </a>
                                    <?php endif; ?>

                                    <?php if ($canDelete && $model['deleted_at'] === null) : ?>
                                        <a href="#" class="dropdown-item invoice-delete" data-toggle="modal" data-target="#ideal-mini-modal-delete" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "delete") ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>

<div id="one" data-text="<?= Yii::t("app", "add_invoice_new") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "edit_invoice") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "delete_invoice") ?>"></div>
<div id="four" data-text="<?= Yii::t("app", "view_invoice") ?>"></div>
<div id="six" data-text="<?= Yii::t("app", "product_return") ?>"></div>

<?php
$js = <<<JS
(function($) {
    var one = $('#one').data('text');
    var two = $('#two').data('text');
    var three = $('#three').data('text');
    var four = $('#four').data('text');
    var six = $('#six').data('text');

    function initializeDataTable() {
        if ($.fn.DataTable.isDataTable('#invoice-grid-view')) {
            $('#invoice-grid-view').DataTable().destroy();
        }

        $('#invoice-grid-view').DataTable({
            "language": {
                "search": "{$searchLabel}",
                "lengthMenu": "{$lengthMenuLabel}",
                "zeroRecords": "{$zeroRecordsLabel}",
                "info": "{$infoLabel}",
                "infoEmpty": "{$infoEmptyLabel}",
                "infoFiltered": "{$infoFilteredLabel}"
            },
            "pageLength": 50,
            "order": [[0, 'desc']],
            "columnDefs": [
                {
                    "targets": [4, 5, 6, 7, 8, 10],
                    "orderable": false
                },

            ]
        });
    }

    $(document).off('pjax:end').on('pjax:end', function() {
        initializeDataTable();
    });


    function initializeDropdown() {
        $(document).off('click.dropdown').on('click.dropdown', '.dropdown-toggle', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var dropdownMenu = $(this).siblings('.dropdown-menu');
            $('.dropdown-menu').not(dropdownMenu).removeClass('show');
            dropdownMenu.toggleClass('show');
        });

        $(document).off('click.dropdown-item').on('click.dropdown-item', '.dropdown-item', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        $(document).off('click.dropdown-close').on('click.dropdown-close', function(e) {
            if (!$(e.target).closest('.dropdown').length) {
                $('.dropdown-menu').removeClass('show');
            }
        });
    }

    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            dropdownParent: $('#ideal-mini-modal'),
            language: {
                noResults: function() {
                    return "Натижа топилмади";
                }
            }
        });
    }

    function initializeInvoiceCreate() {
        $(document).off('click.invoice-create').on('click.invoice-create', '.invoice-create', function() {
            $.ajax({
                url: '/backend/invoice/create',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    if(response.status === 'success') {
                        $('#ideal-mini-modal .modal-title').html(one);
                        $('#ideal-mini-modal .modal-body').html(response.content);
                        $('#ideal-mini-modal .mini-button').addClass("invoice-create-button");
                        initializeSelect2();
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.invoice-create-button').on('click.invoice-create-button', '.invoice-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#invoice-create-form').serializeArray();

                formData = formData.filter(item => {
                    if (item.name.includes('[bonus]')) {
                        if ((item.value === '' && item.name.includes('[product_id]')) ||
                            (item.value === '' && item.name.includes('[quantity]'))) {
                            return false;
                        }
                    }
                    return true;
                });

                var formDataString = $.param(formData);

                $.ajax({
                    url: '/backend/invoice/create',
                    type: 'POST',
                    data: formDataString,
                    dataType: 'json',
                    success: function(response) {
                        $('.error-message').text('');

                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            iziToast.success({
                                title: 'Успешно',
                                message: 'Накладная успешно создана',
                                position: 'topRight'
                            });
                            $.pjax.reload({
                                container: '#invoice-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        } else if (response.message) {
                            // Очищаем предыдущие ошибки
                            $('.quantity-error, .bonus-error').hide().empty();
                            $('.error-message').empty();

                            // Проверяем, является ли response.message объектом
                            if (typeof response.message === 'object' && response.message !== null) {
                                $.each(response.message, function(field, errors) {
                                    const errorText = Array.isArray(errors) ? errors.join(', ') : errors;

                                    if (field === 'products') {
                                        // Показываем ошибку в блоке quantity-error
                                        let displayText = errorText;

                                        // Заменяем ID продукта на его название
                                        const productIdMatch = errorText.match(/product #(\d+)/);
                                        if (productIdMatch) {
                                            const productId = productIdMatch[1];
                                            const productName = window.productsList[productId];
                                            if (productName) {
                                                displayText = errorText.replace(`product #\${productId}`, `"\${productName}"`);
                                            }
                                        }

                                        $('.quantity-error')
                                            .html(displayText)
                                            .show()
                                            .css({
                                                'display': 'block',
                                                'margin': '10px 0',
                                                'padding': '10px 15px',
                                                'border-radius': '4px',
                                                'background-color': '#f8d7da',
                                                'border': '1px solid #f5c6cb',
                                                'color': '#721c24'
                                            });
                                    } else if (field === 'bonus') {
                                        // Показываем ошибку бонуса в блоке bonus-error
                                        $('.bonus-error')
                                            .html(errorText)
                                            .show()
                                            .css({
                                                'display': 'block',
                                                'margin': '10px 0',
                                                'padding': '10px 15px',
                                                'border-radius': '4px',
                                                'background-color': '#f8d7da',
                                                'border': '1px solid #f5c6cb',
                                                'color': '#721c24'
                                            });
                                    } else if (field) {
                                        // Добавляем стили для поля с ошибкой
                                        $(`#\${field}-error`)
                                            .text(errorText)
                                            .show()
                                            .css({
                                                'color': '#721c24',
                                                'display': 'block',
                                                'margin-top': '5px',
                                                'font-weight': 'bold'
                                            });

                                        // Добавляем класс is-invalid для поля
                                        if (field === 'driver') {
                                            $('#driver-select').addClass('is-invalid');
                                            // Прокручиваем к полю с ошибкой
                                            setTimeout(function() {
                                                $('html, body').animate({
                                                    scrollTop: $('#driver-select').offset().top - 100
                                                }, 500);
                                            }, 100);
                                        }
                                    }
                                });
                            } else {
                                // Если response.message не объект, показываем его как простую ошибку
                                $('.error-message')
                                    .text(response.message)
                                    .show()
                                    .css('color', '#721c24');
                            }

                            button.prop('disabled', false);
                        }
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    function initializeInvoiceUpdate() {
    $(document).off('click.invoice-update').on('click.invoice-update', '.invoice-update', function() {
        var invoiceId = $(this).data('id');
        $.ajax({
            url: '/backend/invoice/update',
            type: 'GET',
            data: { id: invoiceId },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    $('#ideal-mini-modal .modal-title').html(two); // Предполагается, что 'two' определена где-то в коде
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("invoice-update-button");
                    initializeSelect2();
                }
            },
            error: function(xhr, textStatus, errorThrown) {
                console.error('AJAX Error:', xhr.statusText, errorThrown);
            }
        });
    });

    $(document).off('click.invoice-update-button').on('click.invoice-update-button', '.invoice-update-button', function() {
        var button = $(this);
        if (!button.prop('disabled')) {
            button.prop('disabled', true);
            var formData = $('#invoice-update-form').serializeArray();

            // Фильтрация данных формы для исключения пустых значений в бонусах
            formData = formData.filter(item => {
                if (item.name.includes('[bonus]')) {
                    if ((item.value === '' && item.name.includes('[product_id]')) ||
                        (item.value === '' && item.name.includes('[quantity]'))) {
                        return false;
                    }
                }
                return true;
            });

            var formDataString = $.param(formData);

            $.ajax({
                url: '/backend/invoice/update',
                type: 'POST',
                data: formDataString,
                dataType: 'json',
                success: function(response) {
                    // Очищаем предыдущие ошибки
                    $('.error-message').text('');
                    $('.quantity-error, .bonus-error').hide().empty();

                    if (response.status === 'success') {
                        button.prop('disabled', false);
                        $('.close').trigger('click');
                        iziToast.success({
                            title: 'Успешно',
                            message: 'Накладная успешно обновлена',
                            position: 'topRight'
                        });
                        $.pjax.reload({
                            container: '#invoice-grid-pjax',
                            complete: function() {
                                initializeDataTable();
                            }
                        });
                    } else if (response.message) {
                        // Проверяем, является ли response.message объектом
                        if (typeof response.message === 'object' && response.message !== null) {
                            $.each(response.message, function(field, errors) {
                                const errorText = Array.isArray(errors) ? errors.join(', ') : errors;

                                if (field === 'products') {
                                    // Показываем ошибку в блоке quantity-error
                                    let displayText = errorText;

                                    // Заменяем ID продукта на его название, если доступен список продуктов
                                    const productIdMatch = errorText.match(/product #(\d+)/);
                                    if (productIdMatch) {
                                        const productId = productIdMatch[1];
                                        const productName = window.productsList[productId];
                                        if (productName) {
                                            displayText = errorText.replace(`product #\${productId}`, `"\${productName}"`);
                                        }
                                    }

                                    $('.quantity-error')
                                        .html(displayText)
                                        .show()
                                        .css({
                                            'display': 'block',
                                            'margin': '10px 0',
                                            'padding': '10px 15px',
                                            'border-radius': '4px',
                                            'background-color': '#f8d7da',
                                            'border': '1px solid #f5c6cb',
                                            'color': '#721c24'
                                        });
                                } else if (field === 'bonus') {
                                    // Показываем ошибку бонуса в блоке bonus-error
                                    $('.bonus-error')
                                        .html(errorText)
                                        .show()
                                        .css({
                                            'display': 'block',
                                            'margin': '10px 0',
                                            'padding': '10px 15px',
                                            'border-radius': '4px',
                                            'background-color': '#f8d7da',
                                            'border': '1px solid #f5c6cb',
                                            'color': '#721c24'
                                        });
                                } else if (field) {
                                    // Ошибки для остальных полей
                                    $(`#\${field}-error`)
                                        .text(errorText)
                                        .show()
                                        .css('color', '#721c24');
                                }
                            });
                        } else {
                            // Если response.message не объект, показываем его как простую ошибку
                            $('.error-message')
                                .text(response.message)
                                .show()
                                .css('color', '#721c24');
                        }

                        button.prop('disabled', false);
                    }
                },
                complete: function() {
                    button.prop('disabled', false);
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                    button.prop('disabled', false);
                }
            });
        }
    });
}
    function initializeInvoiceView() {
        $(document).off('click.invoice-view').on('click.invoice-view', '.invoice-view', function() {
            var invoiceId = $(this).data('id');
            $.ajax({
                url: '/backend/invoice/view',
                type: 'GET',
                data: { id: invoiceId },
                success: function(response) {
                    $('#ideal-large-modal-without-save .modal-body').html(response);
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });
    }

    function initializeInvoicePrint() {
        $(document).off('click.invoice-print-option').on('click.invoice-print-option', '.invoice-print-option', function(e) {
            e.preventDefault();
            var invoiceId = $(this).data('id');
            var withPrice = $(this).data('with-price'); // Получаем значение из data-атрибута

            $.ajax({
                url: '/backend/invoice/print',
                type: 'GET',
                data: {
                    id: invoiceId,
                    withPrice: withPrice // Передаем выбор пользователя
                },
                success: function(response) {
                    // Создаем и добавляем iframe
                    var iframe = $('<iframe>')
                        .hide()
                        .appendTo('body');

                    // Получаем документ iframe
                    var iframeDoc = iframe[0].contentWindow.document;

                    // Записываем HTML в iframe
                    iframeDoc.write(`
                        <!DOCTYPE html>
                        <html>
                            <head>
                                <title>Накладная #\${invoiceId}</title>
                                <meta charset="utf-8">
                            </head>
                            <body>
                                \${response}
                            </body>
                        </html>
                    `);

                    iframeDoc.close();

                    // Ждем загрузки контента и печатаем
                    iframe.on('load', function() {
                        // Печатаем содержимое iframe
                        iframe[0].contentWindow.print();

                        // Удаляем iframe после небольшой задержки
                        setTimeout(function() {
                            iframe.remove();
                        }, 100);
                    });
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('Ошибка AJAX:', xhr.statusText, errorThrown);
                }
            });
        });
    }

    function initialInvoiceDelete(){
        $(document).off('click.invoice-delete').on('click.invoice-delete', '.invoice-delete', function() {
            var invoiceId = $(this).data('id');
            $.ajax({
                url: '/backend/invoice/delete',
                type: 'GET',
                data: { id: invoiceId },
                dataType: 'json',
                success: function(response) {
                    if(response.status === 'success') {
                        $('#ideal-mini-modal-delete .modal-title').html(three);
                        $('#ideal-mini-modal-delete .modal-body').html(response.content);
                        $('#ideal-mini-modal-delete .mini-button').addClass("invoice-delete-button");
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.invoice-delete-button').on('click.invoice-delete-button', '.invoice-delete-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#invoice-delete-form').serialize();
                $.ajax({
                    url: '/backend/invoice/delete',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        $('.error-message').text('');

                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#invoice-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });
                        }
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }



    function initialProductReturn(){
        $(document).off('click.product-return').on('click.product-return', '.product-return', function() {
            var invoiceId = $(this).data('id');
            $.ajax({
                url: '/backend/invoice/product-return',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if(response.status === 'success') {
                        $('#ideal-mini-modal .modal-title').html(six);
                        $('#ideal-mini-modal .modal-body').html(response.content);
                        $('#ideal-mini-modal .mini-button').addClass("product-return-button");
                        initializeSelect2();
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.product-return-button').on('click.product-return-button', '.product-return-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#product-return-form').serialize();
                $.ajax({
                    url: '/backend/invoice/product-return',
                    type: 'POST',
                    data: formData,
                    dataType: 'json',
                    success: function(response) {
                        $('.error-message').text('');

                        if (response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({
                                container: '#invoice-grid-pjax',
                                complete: function() {
                                    initializeDataTable();
                                }
                            });

                            iziToast.success({
                                message: response.message,
                                position: 'topRight',
                                timeout: 2000
                            });
                        }
                    },
                    complete: function() {
                        button.prop('disabled', false);
                    }
                });
            }
        });
    }

    // Инициализация печатной накладной
    function initializePrintInvoice() {
        $(document).off('click.print-invoice-btn').on('click.print-invoice-btn', '.print-invoice-btn', function() {
            var invoiceId = $(this).data('id');

            $.ajax({
                url: '/backend/print-invoice/view',
                type: 'GET',
                data: { id: invoiceId },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        // Создаем новое окно для печати
                        var printWindow = window.open('', '_blank', 'width=800,height=600');

                        // Создаем HTML для печати
                        var printHTML = \`
                            <!DOCTYPE html>
                            <html>
                            <head>
                                <meta charset="utf-8">
                                <title>Печатная накладная #\${invoiceId}</title>
                                <style>
                                    @page {
                                        size: A4 landscape;
                                        margin: 15mm;
                                    }

                                    body {
                                        margin: 0;
                                        padding: 0;
                                        background: white;
                                        color: black;
                                        font-family: 'Courier New', monospace;
                                    }

                                    .print-invoice-view {
                                        margin: 0;
                                        padding: 0;
                                        max-width: 400px;
                                        width: 400px;
                                        background: white;
                                        box-shadow: none;
                                        border: none;
                                        font-size: 10px;
                                        line-height: 1.2;
                                        float: left;
                                    }

                                    .print-invoice-header {
                                        text-align: left;
                                        margin-bottom: 10px;
                                        border-bottom: 1px solid #000;
                                        padding-bottom: 8px;
                                    }

                                    .print-invoice-number {
                                        font-size: 14px;
                                        font-weight: bold;
                                        margin-bottom: 5px;
                                    }

                                    .editable-number {
                                        font-size: 14px;
                                        font-weight: bold;
                                        border: none;
                                        background: transparent;
                                        width: auto;
                                    }

                                    .date-line {
                                        font-size: 8px;
                                        margin-bottom: 8px;
                                    }

                                    .client-info {
                                        margin-bottom: 8px;
                                        font-size: 8px;
                                        border-bottom: 1px solid #000;
                                        padding-bottom: 6px;
                                    }

                                    .products-table {
                                        width: 100%;
                                        border-collapse: collapse;
                                        margin-bottom: 8px;
                                        font-size: 8px;
                                    }

                                    .products-table th,
                                    .products-table td {
                                        border: 1px solid #000;
                                        padding: 2px 4px;
                                        text-align: center;
                                        font-size: 8px;
                                    }

                                    .products-table th {
                                        font-weight: bold;
                                        background-color: #f5f5f5;
                                    }

                                    .products-table td:first-child {
                                        text-align: left;
                                        font-size: 8px;
                                    }

                                    .total-info {
                                        text-align: left;
                                        font-weight: bold;
                                        margin-top: 8px;
                                        border-top: 1px solid #000;
                                        padding-top: 6px;
                                        font-size: 10px;
                                    }

                                    .prices-section {
                                        margin-top: 10px;
                                        font-size: 8px;
                                    }

                                    .price-sum {
                                        text-align: left;
                                        font-weight: bold;
                                        margin-bottom: 2px;
                                        padding: 2px;
                                        border-bottom: 1px solid #ccc;
                                    }

                                    .no-print {
                                        display: none;
                                    }
                                </style>
                            </head>
                            <body>
                                \${response.content}
                            </body>
                            </html>
                        \`;

                        // Записываем HTML в новое окно
                        printWindow.document.write(printHTML);
                        printWindow.document.close();

                        // Ждем загрузки и сразу печатаем
                        printWindow.onload = function() {
                            printWindow.print();
                            printWindow.close();
                        };
                    } else {
                        iziToast.error({
                            title: 'Ошибка',
                            message: response.message || 'Произошла ошибка при загрузке печатной накладной',
                            position: 'topRight'
                        });
                    }
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                    iziToast.error({
                        title: 'Ошибка',
                        message: 'Не удалось загрузить печатную накладную',
                        position: 'topRight'
                    });
                }
            });
        });
    }



    initializeDataTable();
    initializeDropdown();
    initializeSelect2();
    initializeInvoiceCreate();
    initializeInvoiceUpdate();
    initializeInvoiceView();
    initializeInvoicePrint();
    initialInvoiceDelete();
    initialProductReturn();
    initializePrintInvoice();
})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
