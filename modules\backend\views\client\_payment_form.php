<?php

use app\common\models\PaymentType;
use app\common\models\Cashbox;
use app\common\models\CurrencyCourse;
use app\assets\AppAsset;

AppAsset::register($this);

?>

<div class="client-form">
    <form id="client-pay-form">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="cashbox_id"><?= Yii::t('app', 'cashbox') ?></label>
                    <select id="cashbox_id" class="form-control select2" name="ClientPayments[cashbox_id]">
                        <option value=""><?= Yii::t('app', 'select') ?></option>
                        <?php 
                        $cashboxes = Cashbox::find()->where(['deleted_at' => null])->all();
                        foreach ($cashboxes as $cashbox): ?>
                            <option value="<?= $cashbox->id ?>" 
                                    data-currency-id="<?= $cashbox->currency_id ?>"
                                    data-currency-name="<?= $cashbox->currency ? $cashbox->currency->name : 'N/A' ?>">
                                <?= $cashbox->title ?> (<?= $cashbox->currency ? $cashbox->currency->name : 'Без валюты' ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="error-container" id="cashbox_id-error"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="type"><?= Yii::t('app', 'payment_type') ?></label>
                    <select id="type" class="form-control select2" name="ClientPayments[type]">
                        <option value=""><?= Yii::t('app', 'select_type') ?></option>
                        <?php
                        foreach (PaymentType::getTypeLabels() as $key => $value): ?>
                            <option value="<?= $key ?>"><?= $value ?></option>
                        <?php endforeach; ?>
                    </select>
                    <div class="error-container" id="type-error"></div>
                </div>
            </div>
        </div>

        <div class="form-group mt-3">
            <label for="summa"><?= Yii::t('app', 'amount') ?></label>
            <input type="text" class="form-control formatted-numeric-input" id="summa" name="ClientPayments[summa]">
            <div class="error-container" id="summa-error"></div>
        </div>

        <!-- Информация о конвертации валют -->
        <div id="payment-conversion-info" class="row" style="display: none;">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <h6><i class="fas fa-exchange-alt"></i> <?= Yii::t('app', 'currency_conversion') ?></h6>
                    <div id="payment-conversion-details"></div>
                </div>
            </div>
        </div>

        <input type="hidden" name="ClientPayments[client_id]" value="<?= $model->client_id ?>">
    </form>
</div>

<script>
// Получаем курсы валют для JavaScript
<?php
$courses = [];
$allCourses = CurrencyCourse::find()
    ->where(['<=', 'start_date', date('Y-m-d')])
    ->andWhere([
        'or',
        ['>', 'end_date', date('Y-m-d')],
        ['end_date' => null]
    ])
    ->andWhere(['deleted_at' => null])
    ->all();

foreach ($allCourses as $course) {
    $courses[$course->currency_id] = $course->course;
}

// Убеждаемся что курс базовой валюты (доллар) присутствует
if (!isset($courses[1])) {
    $courses[1] = 1; // Доллар - базовая валюта
}
?>
window.paymentCurrencyCourses = <?= json_encode($courses) ?>;

$(document).ready(function() {
    function updatePaymentConversionInfo() {
        var cashboxSelect = $('#cashbox_id');
        var amountInput = $('#summa');
        
        var cashboxCurrencyId = cashboxSelect.find(':selected').data('currency-id');
        var cashboxCurrencyName = cashboxSelect.find(':selected').data('currency-name');
        
        // Базовая валюта для платежей клиентов - сомы (ID=2)
        var clientCurrencyId = 2;
        var clientCurrencyName = 'Сўм';
        
        var amountValue = amountInput.val() || '';
        var cleanAmount = amountValue.replace(/[\s\u00A0]/g, '').replace(',', '.');
        var amount = parseFloat(cleanAmount) || 0;
        
        if (!cashboxCurrencyId || amount <= 0) {
            $('#payment-conversion-info').hide();
            return;
        }
        
        if (cashboxCurrencyId == clientCurrencyId) {
            $('#payment-conversion-info').hide();
            return;
        }
        
        // Рассчитываем конвертацию (из сом клиента в валюту кассы)
        var convertedAmount = calculatePaymentConversion(amount, clientCurrencyId, cashboxCurrencyId);
        
        if (convertedAmount !== null) {
            var details = `
                <p><strong>Оплата в ${clientCurrencyName}:</strong> ${formatNumber(amount)} ${clientCurrencyName}</p>
                <p><strong>Будет зачислено в кассу:</strong> ${formatNumber(convertedAmount.amount)} ${cashboxCurrencyName}</p>
                <p><strong>Курс:</strong> 1 ${getCurrencyName(1)} = ${formatNumber(window.paymentCurrencyCourses[cashboxCurrencyId] || window.paymentCurrencyCourses[2])} ${getCurrencyName(cashboxCurrencyId)}</p>
            `;
            
            $('#payment-conversion-details').html(details);
            $('#payment-conversion-info').show();
        } else {
            $('#payment-conversion-details').html('<p class="text-danger">Курс валюты не найден</p>');
            $('#payment-conversion-info').show();
        }
    }
    
    function calculatePaymentConversion(amount, fromCurrencyId, toCurrencyId) {
        var baseCurrencyId = 1; // Доллар
        
        if (fromCurrencyId == toCurrencyId) {
            return { amount: amount, rate: 1 };
        }
        
        if (fromCurrencyId == baseCurrencyId) {
            var course = window.paymentCurrencyCourses[toCurrencyId];
            if (!course) return null;
            return { amount: amount * course, rate: course };
        } else if (toCurrencyId == baseCurrencyId) {
            var course = window.paymentCurrencyCourses[fromCurrencyId];
            if (!course) return null;
            return { amount: amount / course, rate: 1 / course };
        } else {
            var fromCourse = window.paymentCurrencyCourses[fromCurrencyId];
            var toCourse = window.paymentCurrencyCourses[toCurrencyId];
            if (!fromCourse || !toCourse) return null;
            
            var amountInDollars = amount / fromCourse;
            var finalAmount = amountInDollars * toCourse;
            return { amount: finalAmount, rate: toCourse / fromCourse };
        }
    }
    
    function getCurrencyName(currencyId) {
        return currencyId == 1 ? 'Dollar' : 'Сўм';
    }
    
    function formatNumber(num) {
        return Number(num).toLocaleString('ru-RU', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }
    
    // Обновляем информацию при изменении полей
    $('#cashbox_id, #summa').on('change keyup', updatePaymentConversionInfo);
});
</script>