<?php
use app\common\models\PaymentType;
use app\common\models\Cashbox;
use app\common\models\CurrencyCourse;
?>

<div class="expenses-form">
    <form id="expenses-update-form" method="post">
        <div class="form-group">
            <label for="expense_type_id"><?= Yii::t('app', 'Expense type') ?></label>
            <select id="expense_type_id" name="Expenses[expense_type_id]" class="form-control select2" required>
                <option value=""><?= Yii::t('app', 'select_expense_type') ?></option>
                <?php foreach ($expenseTypes as $expenseType): ?>
                    <option value="<?= $expenseType->id ?>" <?= $model->expense_type_id == $expenseType->id ? 'selected' : '' ?>><?= $expenseType->name ?></option>
                <?php endforeach; ?>
            </select>
            <div class="help-block"></div>
        </div>

        <div class="form-group">
            <label for="summa"><?= Yii::t('app', 'amount') ?></label>
            <input type="text" id="summa" name="Expenses[summa]" class="form-control formatted-numeric-input" step="0.01" required value="<?= $model->summa ?>">
            <div class="help-block"></div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="cashbox_id"><?= Yii::t('app', 'cashbox') ?></label>
                    <select id="cashbox_id" name="Expenses[cashbox_id]" class="form-control select2" required>
                        <option value=""><?= Yii::t('app', 'select') ?></option>
                        <?php 
                        $cashboxes = Cashbox::find()->where(['deleted_at' => null])->all();
                        foreach ($cashboxes as $cashbox): ?>
                            <option value="<?= $cashbox->id ?>" 
                                    data-currency-id="<?= $cashbox->currency_id ?>"
                                    data-currency-name="<?= $cashbox->currency ? $cashbox->currency->name : 'N/A' ?>"
                                    <?= $model->cashbox_id == $cashbox->id ? 'selected' : '' ?>>
                                <?= $cashbox->title ?> (<?= $cashbox->currency ? $cashbox->currency->name : 'Без валюты' ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="help-block"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="payment_type"><?= Yii::t('app', 'Payment type') ?></label>
                    <select id="payment_type" name="Expenses[payment_type]" class="form-control select2" required>
                        <option value=""><?= Yii::t('app', 'select_type') ?></option>
                        <?php foreach (PaymentType::getTypeLabels() as $value => $label): ?>
                            <option value="<?= $value ?>" data-value="<?= $value ?>" <?= $model->payment_type == $value ? 'selected' : '' ?>><?= $label ?></option>
                        <?php endforeach; ?>
                    </select>
                    <div class="help-block"></div>
                </div>
            </div>
        </div>

        <!-- Информация о конвертации валют -->
        <div id="expense-conversion-info" class="row" style="display: none;">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <h6><i class="fas fa-exchange-alt"></i> <?= Yii::t('app', 'currency_conversion') ?></h6>
                    <div id="expense-conversion-details"></div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="description"><?= Yii::t('app', 'description') ?></label>
            <textarea id="description" name="Expenses[description]" class="form-control" rows="3"><?= $model->description ?></textarea>
            <div class="help-block"></div>
        </div>

        <input type="hidden" name="Expenses[id]" value="<?= $model->id ?>">
    </form>
    <div id="form-error-message" style="display: none;"></div>
</div>

<script>
// Получаем курсы валют для JavaScript
<?php
$courses = [];
$allCourses = CurrencyCourse::find()
    ->where(['<=', 'start_date', date('Y-m-d')])
    ->andWhere([
        'or',
        ['>', 'end_date', date('Y-m-d')],
        ['end_date' => null]
    ])
    ->andWhere(['deleted_at' => null])
    ->all();

foreach ($allCourses as $course) {
    $courses[$course->currency_id] = $course->course;
}

// Убеждаемся что курс базовой валюты (доллар) присутствует
if (!isset($courses[1])) {
    $courses[1] = 1; // Доллар - базовая валюта
}
?>
window.expenseUpdateCurrencyCourses = <?= json_encode($courses) ?>;

$(document).ready(function() {
    $('.select2').select2({
        width: '100%',
        placeholder: '<?= Yii::t("app", "select") ?>',
        allowClear: true,
        dropdownParent: $('#ideal-mini-modal'),
        language: {
            noResults: function() {
                return "<?= Yii::t('app', 'Nothing found') ?>";
            },
            searching: function() {
                return "<?= Yii::t('app', 'Searching...') ?>";
            }
        },
        minimumInputLength: 0,
        minimumResultsForSearch: 1
    });

    // Инициализация форматирования числовых полей
    $('.formatted-numeric-input').on('input', function() {
        var value = $(this).val().replace(/\s+/g, '');
        var formattedValue = value.replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
        $(this).val(formattedValue);
    });

    function updateExpenseUpdateConversionInfo() {
        var cashboxSelect = $('#cashbox_id');
        var amountInput = $('#summa');
        
        var cashboxCurrencyId = cashboxSelect.find(':selected').data('currency-id');
        var cashboxCurrencyName = cashboxSelect.find(':selected').data('currency-name');
        
        // Базовая валюта для расходов - сомы (ID=2)
        var expenseCurrencyId = 2;
        var expenseCurrencyName = 'Сўм';
        
        var amountValue = amountInput.val() || '';
        var cleanAmount = amountValue.replace(/[\s\u00A0]/g, '').replace(',', '.');
        var amount = parseFloat(cleanAmount) || 0;
        
        if (!cashboxCurrencyId || amount <= 0) {
            $('#expense-conversion-info').hide();
            return;
        }
        
        if (cashboxCurrencyId == expenseCurrencyId) {
            $('#expense-conversion-info').hide();
            return;
        }
        
        // Рассчитываем конвертацию (из сом расхода в валюту кассы)
        var convertedAmount = calculateExpenseUpdateConversion(amount, expenseCurrencyId, cashboxCurrencyId);
        
        if (convertedAmount !== null) {
            var details = `
                <p><strong>Расход в ${expenseCurrencyName}:</strong> ${formatNumber(amount)} ${expenseCurrencyName}</p>
                <p><strong>Будет списано с кассы:</strong> ${formatNumber(convertedAmount.amount)} ${cashboxCurrencyName}</p>
                <p><strong>Курс:</strong> 1 ${getCurrencyName(1)} = ${formatNumber(window.expenseUpdateCurrencyCourses[cashboxCurrencyId] || window.expenseUpdateCurrencyCourses[2])} ${getCurrencyName(cashboxCurrencyId)}</p>
            `;
            
            $('#expense-conversion-details').html(details);
            $('#expense-conversion-info').show();
        } else {
            $('#expense-conversion-details').html('<p class="text-danger">Курс валюты не найден</p>');
            $('#expense-conversion-info').show();
        }
    }
    
    function calculateExpenseUpdateConversion(amount, fromCurrencyId, toCurrencyId) {
        var baseCurrencyId = 1; // Доллар
        
        if (fromCurrencyId == toCurrencyId) {
            return { amount: amount, rate: 1 };
        }
        
        if (fromCurrencyId == baseCurrencyId) {
            var course = window.expenseUpdateCurrencyCourses[toCurrencyId];
            if (!course) return null;
            return { amount: amount * course, rate: course };
        } else if (toCurrencyId == baseCurrencyId) {
            var course = window.expenseUpdateCurrencyCourses[fromCurrencyId];
            if (!course) return null;
            return { amount: amount / course, rate: 1 / course };
        } else {
            var fromCourse = window.expenseUpdateCurrencyCourses[fromCurrencyId];
            var toCourse = window.expenseUpdateCurrencyCourses[toCurrencyId];
            if (!fromCourse || !toCourse) return null;
            
            var amountInDollars = amount / fromCourse;
            var finalAmount = amountInDollars * toCourse;
            return { amount: finalAmount, rate: toCourse / fromCourse };
        }
    }
    
    function getCurrencyName(currencyId) {
        return currencyId == 1 ? 'Dollar' : 'Сўм';
    }
    
    function formatNumber(num) {
        return Number(num).toLocaleString('ru-RU', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }
    
    // Обновляем информацию при изменении полей
    $('#cashbox_id, #summa').on('change keyup', updateExpenseUpdateConversionInfo);

    // Показываем информацию о конвертации при загрузке если нужно
    updateExpenseUpdateConversionInfo();
});
</script>
