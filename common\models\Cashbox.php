<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "cashbox".
 *
 * @property int $id
 * @property string|null $title
 * @property int|null $currency_id
 * @property float|null $balance
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property Currency $currency
 * @property CashboxDetail[] $cashboxDetails
 * @property CashboxPaymentType[] $cashboxPaymentTypes
 * @property PaymentType[] $paymentTypes
 */
class Cashbox extends \yii\db\ActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'cashbox';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['title'], 'required'],
            [['currency_id'], 'required'],
            [['currency_id'], 'integer'],
            [['title'], 'string', 'max' => 255],
            [['balance'], 'number'],
            [['created_at', 'deleted_at'], 'safe'],
            [['title', 'currency_id'], 'unique', 'targetAttribute' => ['title', 'currency_id'], 
                'filter' => ['deleted_at' => null], 
                'message' => 'Касса с таким названием и валютой уже существует.'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'title' => 'Название кассы',
            'currency_id' => 'Валюта',
            'balance' => 'Баланс',
            'created_at' => 'Создана',
            'deleted_at' => 'Удалена',
        ];
    }

    /**
     * Gets query for [[Currency]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCurrency()
    {
        return $this->hasOne(Currency::class, ['id' => 'currency_id']);
    }

    /**
     * Gets query for [[CashboxDetails]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCashboxDetails()
    {
        return $this->hasMany(CashboxDetail::class, ['cashbox_id' => 'id']);
    }

    /**
     * Gets query for [[CashboxPaymentTypes]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getCashboxPaymentTypes()
    {
        return $this->hasMany(CashboxPaymentType::class, ['cashbox_id' => 'id'])
            ->where(['cashbox_payment_type.deleted_at' => null]);
    }

    /**
     * Gets query for [[PaymentTypes]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getPaymentTypes()
    {
        return $this->hasMany(PaymentType::class, ['id' => 'payment_type_id'])
            ->via('cashboxPaymentTypes')
            ->where(['payment_type.deleted_at' => null]);
    }

    /**
     * Находит кассу по типу платежа и валюте
     *
     * @param int $paymentTypeId
     * @param int $currencyId
     * @return Cashbox|null
     */
    public static function findByPaymentTypeAndCurrency($paymentTypeId, $currencyId)
    {
        return self::find()
            ->joinWith('cashboxPaymentTypes cpt')
            ->where([
                'cashbox.currency_id' => $currencyId,
                'cpt.payment_type_id' => $paymentTypeId,
                'cashbox.deleted_at' => null,
                'cpt.deleted_at' => null
            ])
            ->one();
    }

    /**
     * Возвращает строку с названиями всех связанных типов платежей
     *
     * @return string
     */
    public function getPaymentTypesString()
    {
        $paymentTypes = $this->paymentTypes;
        if (empty($paymentTypes)) {
            return 'Не связано ни с одним типом';
        }
        
        $names = [];
        foreach ($paymentTypes as $paymentType) {
            $names[] = PaymentType::getTypeDescription($paymentType->type);
        }
        
        return implode(', ', $names);
    }

    /**
     * Возвращает массив касс для select dropdown (новая структура)
     * @return array
     */
    public static function getNameLabels()
    {
        $cashboxes = self::find()
            ->select(['id', 'title', 'currency.name as currency_name'])
            ->joinWith('currency')
            ->where(['cashbox.deleted_at' => null])
            ->asArray()
            ->all();

        $result = [];
        foreach ($cashboxes as $cashbox) {
            $result[$cashbox['id']] = $cashbox['title'] . ' (' . $cashbox['currency_name'] . ')';
        }
        return $result;
    }

    /**
     * Возвращает название кассы (новая структура - просто возвращает переданное название)
     * @param string $name
     * @return string
     */
    public static function getNameDescription($name)
    {
        return $name; // В новой структуре у нас уже человекочитаемые названия
    }

    /**
     * Возвращает описание типа платежа (делегируем в PaymentType)
     * @param int $type
     * @return string
     */
    public static function getTypeDescription($type)
    {
        return PaymentType::getTypeDescription($type);
    }

    /**
     * Устаревшие константы для совместимости со старым кодом
     * TODO: Удалить после полного перехода на новую структуру
     */
    const CASH = 1;
    const TRANSFER = 2;
}
