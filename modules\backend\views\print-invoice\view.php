<?php
use yii\helpers\Html;
?>

<div class="print-invoice-view">
    <style>
        .print-invoice-view {
            max-width: 800px;
            margin: 20px auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.3;
            background: white;
            padding: 20px;
            border: 1px solid #ddd;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .print-invoice-header {
            text-align: left;
            margin-bottom: 20px;
            border-bottom: 2px solid #000;
            padding-bottom: 15px;
        }
        .print-invoice-number {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .editable-number {
            border: none;
            background: transparent;
            font-size: 20px;
            font-weight: bold;
            text-align: left;
            width: 60px;
        }
        .date-line {
            font-size: 11px;
            margin-bottom: 15px;
        }
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .products-table th,
        .products-table td {
            border: 1px solid #000;
            padding: 6px 8px;
            text-align: center;
            font-size: 11px;
        }
        .products-table th {
            font-weight: bold;
            background-color: #f5f5f5;
        }
        .products-table td:first-child {
            text-align: left;
            font-size: 11px;
        }
        .client-info {
            margin-bottom: 15px;
            font-size: 11px;
            border-bottom: 1px solid #000;
            padding-bottom: 12px;
        }
        .total-info {
            text-align: left;
            font-weight: bold;
            margin-top: 15px;
            border-top: 2px solid #000;
            padding-top: 12px;
            font-size: 13px;
        }
        .prices-section {
            margin-top: 25px;
            font-size: 11px;
        }
        .price-sum {
            text-align: left;
            font-weight: bold;
            margin-bottom: 4px;
            padding: 4px;
            border-bottom: 1px solid #ccc;
        }
        .no-print {
            margin-top: 15px;
            text-align: center;
        }
        @media print {
            @page {
                size: A4 landscape;
                margin: 15mm;
            }

            * {
                visibility: hidden;
            }

            .print-invoice-view,
            .print-invoice-view * {
                visibility: visible;
            }

            body {
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                color: black !important;
            }

            .modal,
            .modal-dialog,
            .modal-content,
            .modal-body {
                all: unset !important;
                display: block !important;
                position: static !important;
                margin: 0 !important;
                padding: 0 !important;
                background: white !important;
                border: none !important;
                box-shadow: none !important;
            }

            .print-invoice-view {
                position: absolute !important;
                top: 0 !important;
                left: 0 !important;
                margin: 0 !important;
                padding: 0 !important;
                max-width: none !important;
                width: 100% !important;
                background: white !important;
                box-shadow: none !important;
                border: none !important;
                font-size: 12px !important;
                line-height: 1.3 !important;
            }

            .no-print {
                display: none !important;
            }
            .print-invoice-header {
                text-align: left !important;
                margin-bottom: 20px !important;
                border-bottom: 2px solid #000 !important;
                padding-bottom: 15px !important;
            }
            .print-invoice-number {
                font-size: 24px !important;
                font-weight: bold !important;
                margin-bottom: 10px !important;
            }
            .editable-number {
                font-size: 24px !important;
                font-weight: bold !important;
                border: none !important;
                background: transparent !important;
                width: auto !important;
            }
            .date-line {
                font-size: 12px !important;
                margin-bottom: 15px !important;
            }
            .client-info {
                margin-bottom: 20px !important;
                font-size: 12px !important;
                border-bottom: 1px solid #000 !important;
                padding-bottom: 15px !important;
            }
            .products-table {
                width: 100% !important;
                border-collapse: collapse !important;
                margin-bottom: 20px !important;
                font-size: 12px !important;
            }
            .products-table th,
            .products-table td {
                border: 1px solid #000 !important;
                padding: 8px 12px !important;
                text-align: center !important;
                font-size: 12px !important;
            }
            .products-table td:first-child {
                text-align: left !important;
                font-size: 12px !important;
            }
            .total-info {
                text-align: left !important;
                font-weight: bold !important;
                margin-top: 15px !important;
                border-top: 2px solid #000 !important;
                padding-top: 15px !important;
                font-size: 14px !important;
            }
            .prices-section {
                margin-top: 25px !important;
                font-size: 12px !important;
            }
            .price-sum {
                text-align: left !important;
                font-weight: bold !important;
                margin-bottom: 5px !important;
                padding: 5px !important;
                border-bottom: 1px solid #ccc !important;
            }
        }
    </style>

    <div class="print-invoice-header">
        <div class="print-invoice-number">
            <input type="number" class="editable-number" id="print-number" value="<?= $printNumber ?>" min="1" max="9999" />
        </div>
        <div class="date-line"><?= date('d/m/Y H:i:s') ?></div>
    </div>

    <div class="client-info">
        <div><strong>КЛИЕНТ:</strong><br><?= Html::encode($sales->client->full_name) ?></div>
        <div><strong>АВТОМОБИЛЬ:</strong> <?= Html::encode($sales->car_number) ?></div>
        <div><strong>ВОДИТЕЛЬ:</strong> <?= Html::encode($sales->driver) ?></div>
    </div>

    <!-- Таблица продукции -->
    <table class="products-table">
        <thead>
            <tr>
                <th style="width: 50%;">ПРОДУКТ</th>
                <th style="width: 25%;">БЛОКИ</th>
                <th style="width: 25%;">ШТ</th>
            </tr>
        </thead>
        <tbody>
            <?php 
            // Порядок отображения как на фото
            $displayOrder = ['0.33', '0.5 gaz', '0.5 bezgaz', '1 gaz', '1 bezgaz', '1.5 gaz', '1.5 bezgaz', '5', '10'];
            $displayNames = [
                '0.33' => '0,33 без газ',
                '0.5 gaz' => '0.5 литр газ', 
                '0.5 bezgaz' => '0.5 без газ',
                '1 gaz' => '1,0 литр газ',
                '1 bezgaz' => '1,0 без газ', 
                '1.5 gaz' => '1.5 литр газ',
                '1.5 bezgaz' => '1.5 без газ',
                '5' => '5 литр',
                '10' => '10 литр'
            ];
            
            // Показываем обычные продукты
            foreach ($displayOrder as $type):
                if (isset($printData['items'][$type])):
                    $blocks = $printData['items'][$type]['blocks'];
                    $quantity = $printData['items'][$type]['quantity'];
            ?>
            <tr>
                <td><?= $displayNames[$type] ?></td>
                <td><?= number_format($blocks, 0) ?></td>
                <td><?= number_format($quantity, 0) ?></td>
            </tr>
            <?php 
                endif;
            endforeach; 
            
            // Показываем бонусные продукты если есть
            if (!empty($printData['bonusItems'])):
            ?>
            <tr style="border-top: 2px solid #000;">
                <td colspan="3" style="text-align: center; font-weight: bold;">БОНУС</td>
            </tr>
            <?php
                foreach ($displayOrder as $type):
                    if (isset($printData['bonusItems'][$type])):
                        $blocks = $printData['bonusItems'][$type]['blocks'];
                        $quantity = $printData['bonusItems'][$type]['quantity'];
            ?>
            <tr>
                <td><?= $displayNames[$type] ?></td>
                <td><?= number_format($blocks, 0) ?></td>
                <td><?= number_format($quantity, 0) ?></td>
            </tr>
            <?php 
                    endif;
                endforeach;
            endif;
            ?>
        </tbody>
    </table>

    <div class="total-info">
        <div>ВЕС: <?= number_format($printData['totalWeight'], 0) ?></div>
    </div>

    <!-- Итоговые суммы по ценам -->
    <div class="prices-section">
        <?php 
        // Рассчитываем итоговые суммы по каждому типу цен
        $priceLabels = ['1-narx', '2-narx', '3-narx', '4-narx', '5-narx'];
        
        foreach ($priceLabels as $priceType):
            $totalSum = 0;
            
            // Рассчитываем сумму для обычных продуктов
            foreach ($printData['items'] as $type => $data) {
                $quantity = $data['quantity'];
                $price = $printData['prices'][$type][$priceType] ?? 0;
                $totalSum += $quantity * $price;
            }
            
            // Добавляем сумму для бонусных продуктов
            foreach ($printData['bonusItems'] as $type => $data) {
                $quantity = $data['quantity'];
                $price = $printData['prices'][$type][$priceType] ?? 0;
                $totalSum += $quantity * $price;
            }
        ?>
        <div class="price-sum"><?= number_format($totalSum, 2, ',', ' ') ?></div>
        <?php endforeach; ?>
    </div>

    <div class="no-print">
        <button type="button" class="btn btn-success btn-sm" onclick="printInvoice()">
            <i class="fas fa-print"></i> Печать
        </button>
    </div>
</div>

<script>
// Функция для печати накладной
function printInvoice() {
    // Создаем новое окно для печати
    var printWindow = window.open('', '_blank', 'width=800,height=600');

    // Получаем содержимое накладной
    var invoiceContent = document.querySelector('.print-invoice-view').outerHTML;

    // Создаем HTML для печати
    var printHTML = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Печатная накладная</title>
            <style>
                @page {
                    size: A4 landscape;
                    margin: 15mm;
                }

                body {
                    margin: 0;
                    padding: 0;
                    background: white;
                    color: black;
                    font-family: 'Courier New', monospace;
                }

                .print-invoice-view {
                    margin: 0;
                    padding: 0;
                    max-width: none;
                    width: 100%;
                    background: white;
                    box-shadow: none;
                    border: none;
                    font-size: 12px;
                    line-height: 1.3;
                }

                .print-invoice-header {
                    text-align: left;
                    margin-bottom: 20px;
                    border-bottom: 2px solid #000;
                    padding-bottom: 15px;
                }

                .print-invoice-number {
                    font-size: 24px;
                    font-weight: bold;
                    margin-bottom: 10px;
                }

                .editable-number {
                    font-size: 24px;
                    font-weight: bold;
                    border: none;
                    background: transparent;
                    width: auto;
                }

                .date-line {
                    font-size: 12px;
                    margin-bottom: 15px;
                }

                .client-info {
                    margin-bottom: 20px;
                    font-size: 12px;
                    border-bottom: 1px solid #000;
                    padding-bottom: 15px;
                }

                .products-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                    font-size: 12px;
                }

                .products-table th,
                .products-table td {
                    border: 1px solid #000;
                    padding: 8px 12px;
                    text-align: center;
                    font-size: 12px;
                }

                .products-table th {
                    font-weight: bold;
                    background-color: #f5f5f5;
                }

                .products-table td:first-child {
                    text-align: left;
                    font-size: 12px;
                }

                .total-info {
                    text-align: left;
                    font-weight: bold;
                    margin-top: 15px;
                    border-top: 2px solid #000;
                    padding-top: 15px;
                    font-size: 14px;
                }

                .prices-section {
                    margin-top: 25px;
                    font-size: 12px;
                }

                .price-sum {
                    text-align: left;
                    font-weight: bold;
                    margin-bottom: 5px;
                    padding: 5px;
                    border-bottom: 1px solid #ccc;
                }

                .no-print {
                    display: none;
                }
            </style>
        </head>
        <body>
            ${invoiceContent}
        </body>
        </html>
    `;

    // Записываем HTML в новое окно
    printWindow.document.write(printHTML);
    printWindow.document.close();

    // Ждем загрузки и печатаем
    printWindow.onload = function() {
        printWindow.print();
        printWindow.close();
    };
}

$(document).ready(function() {
    // Автосохранение номера при изменении
    $('#print-number').on('change', function() {
        var number = $(this).val();
        if (number && number > 0) {
            $.ajax({
                url: '/backend/print-invoice/save-number',
                type: 'POST',
                data: {
                    number: number,
                    '<?= Yii::$app->request->csrfParam ?>': '<?= Yii::$app->request->csrfToken ?>'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        // Увеличиваем номер на 1 для следующей накладной
                        var nextNumber = parseInt(number) + 1;
                        $('#print-number').val(nextNumber);

                        // Сохраняем новый номер
                        $.ajax({
                            url: '/backend/print-invoice/save-number',
                            type: 'POST',
                            data: {
                                number: nextNumber,
                                '<?= Yii::$app->request->csrfParam ?>': '<?= Yii::$app->request->csrfToken ?>'
                            }
                        });
                    }
                }
            });
        }
    });

    // Обработчик события печати
    window.addEventListener('beforeprint', function() {
        document.body.style.background = 'white';
        document.body.style.backgroundColor = 'white';
    });
});
</script>