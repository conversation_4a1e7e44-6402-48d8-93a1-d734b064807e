<?php
use yii\helpers\Html;
?>

<div class="print-invoice-view">
    <style>
        .print-invoice-view {
            max-width: 280px;
            margin: 0;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            line-height: 1.2;
        }
        .print-invoice-header {
            text-align: center;
            margin-bottom: 15px;
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
        }
        .print-invoice-number {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .editable-number {
            border: none;
            background: transparent;
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            width: 40px;
        }
        .date-line {
            font-size: 9px;
            margin-bottom: 10px;
        }
        .products-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        .products-table th,
        .products-table td {
            border: 1px solid #000;
            padding: 2px 4px;
            text-align: center;
            font-size: 9px;
        }
        .products-table th {
            font-weight: bold;
        }
        .products-table td:first-child {
            text-align: left;
            font-size: 8px;
        }
        .client-info {
            margin-bottom: 10px;
            font-size: 9px;
            border-bottom: 1px solid #000;
            padding-bottom: 8px;
        }
        .total-info {
            text-align: center;
            font-weight: bold;
            margin-top: 10px;
            border-top: 1px solid #000;
            padding-top: 8px;
        }
        .prices-section {
            margin-top: 20px;
            font-size: 10px;
        }
        .price-sum {
            text-align: center;
            font-weight: bold;
            margin-bottom: 2px;
            padding: 2px;
            border-bottom: 1px solid #ccc;
        }
        .no-print {
            margin-top: 15px;
            text-align: center;
        }
        @media print {
            @page {
                size: A4 landscape;
                margin: 10mm 10mm 10mm 10mm;
            }
            body {
                margin: 0;
                padding: 0;
                background: white !important;
            }
            .print-invoice-view {
                display: block !important;
                position: relative !important;
                left: 0 !important;
                top: 0 !important;
                margin: 0 !important;
                padding: 0 !important;
                max-width: 280px !important;
                page-break-after: avoid !important;
            }
            .no-print {
                display: none !important;
            }
            /* Скрываем модальное окно при печати */
            #print-invoice-modal {
                display: none !important;
            }
            .modal-backdrop {
                display: none !important;
            }
        }
    </style>

    <div class="print-invoice-header">
        <div class="print-invoice-number">
            <input type="number" class="editable-number" id="print-number" value="<?= $printNumber ?>" min="1" max="9999" />
        </div>
        <div class="date-line"><?= date('d/m/Y H:i:s') ?></div>
    </div>

    <div class="client-info">
        <div><strong>КЛИЕНТ:</strong><br><?= Html::encode($sales->client->full_name) ?></div>
        <div><strong>АВТОМОБИЛЬ:</strong> <?= Html::encode($sales->car_number) ?></div>
        <div><strong>ВОДИТЕЛЬ:</strong> <?= Html::encode($sales->driver) ?></div>
    </div>

    <!-- Таблица продукции -->
    <table class="products-table">
        <thead>
            <tr>
                <th style="width: 50%;">ПРОДУКТ</th>
                <th style="width: 25%;">БЛОКИ</th>
                <th style="width: 25%;">ШТ</th>
            </tr>
        </thead>
        <tbody>
            <?php 
            // Порядок отображения как на фото
            $displayOrder = ['0.33', '0.5 gaz', '0.5 bezgaz', '1 gaz', '1 bezgaz', '1.5 gaz', '1.5 bezgaz', '5', '10'];
            $displayNames = [
                '0.33' => '0,33 без газ',
                '0.5 gaz' => '0.5 литр газ', 
                '0.5 bezgaz' => '0.5 без газ',
                '1 gaz' => '1,0 литр газ',
                '1 bezgaz' => '1,0 без газ', 
                '1.5 gaz' => '1.5 литр газ',
                '1.5 bezgaz' => '1.5 без газ',
                '5' => '5 литр',
                '10' => '10 литр'
            ];
            
            // Показываем обычные продукты
            foreach ($displayOrder as $type):
                if (isset($printData['items'][$type])):
                    $blocks = $printData['items'][$type]['blocks'];
                    $quantity = $printData['items'][$type]['quantity'];
            ?>
            <tr>
                <td><?= $displayNames[$type] ?></td>
                <td><?= number_format($blocks, 0) ?></td>
                <td><?= number_format($quantity, 0) ?></td>
            </tr>
            <?php 
                endif;
            endforeach; 
            
            // Показываем бонусные продукты если есть
            if (!empty($printData['bonusItems'])):
            ?>
            <tr style="border-top: 2px solid #000;">
                <td colspan="3" style="text-align: center; font-weight: bold;">БОНУС</td>
            </tr>
            <?php
                foreach ($displayOrder as $type):
                    if (isset($printData['bonusItems'][$type])):
                        $blocks = $printData['bonusItems'][$type]['blocks'];
                        $quantity = $printData['bonusItems'][$type]['quantity'];
            ?>
            <tr>
                <td><?= $displayNames[$type] ?></td>
                <td><?= number_format($blocks, 0) ?></td>
                <td><?= number_format($quantity, 0) ?></td>
            </tr>
            <?php 
                    endif;
                endforeach;
            endif;
            ?>
        </tbody>
    </table>

    <div class="total-info">
        <div>ВЕС: <?= number_format($printData['totalWeight'], 0) ?></div>
    </div>

    <!-- Итоговые суммы по ценам -->
    <div class="prices-section">
        <?php 
        // Рассчитываем итоговые суммы по каждому типу цен
        $priceLabels = ['1-narx', '2-narx', '3-narx', '4-narx', '5-narx'];
        
        foreach ($priceLabels as $priceType):
            $totalSum = 0;
            
            // Рассчитываем сумму для обычных продуктов
            foreach ($printData['items'] as $type => $data) {
                $quantity = $data['quantity'];
                $price = $printData['prices'][$type][$priceType] ?? 0;
                $totalSum += $quantity * $price;
            }
            
            // Добавляем сумму для бонусных продуктов
            foreach ($printData['bonusItems'] as $type => $data) {
                $quantity = $data['quantity'];
                $price = $printData['prices'][$type][$priceType] ?? 0;
                $totalSum += $quantity * $price;
            }
        ?>
        <div class="price-sum"><?= number_format($totalSum, 2, ',', ' ') ?></div>
        <?php endforeach; ?>
    </div>

    <div class="no-print">
        <button type="button" class="btn btn-success btn-sm" onclick="window.print()">
            <i class="fas fa-print"></i> Печать
        </button>
    </div>
</div>

<script>
$(document).ready(function() {
    // Автосохранение номера при изменении
    $('#print-number').on('change', function() {
        var number = $(this).val();
        if (number && number > 0) {
            $.ajax({
                url: '/backend/print-invoice/save-number',
                type: 'POST',
                data: {
                    number: number,
                    '<?= Yii::$app->request->csrfParam ?>': '<?= Yii::$app->request->csrfToken ?>'
                },
                success: function(response) {
                    if (response.status === 'success') {
                        // Увеличиваем номер на 1 для следующей накладной
                        var nextNumber = parseInt(number) + 1;
                        $('#print-number').val(nextNumber);
                        
                        // Сохраняем новый номер
                        $.ajax({
                            url: '/backend/print-invoice/save-number',
                            type: 'POST',
                            data: {
                                number: nextNumber,
                                '<?= Yii::$app->request->csrfParam ?>': '<?= Yii::$app->request->csrfToken ?>'
                            }
                        });
                    }
                }
            });
        }
    });
});
</script> 