<?php

namespace app\modules\backend\controllers;

use yii\web\Controller;
use app\components\services\ReportService;
use app\modules\backend\services\report\FinanceReportService;
use app\modules\backend\services\report\ProductStockReportService;
use app\modules\backend\services\report\MaterialStockReportService;
use app\modules\backend\services\report\ProductionReportService;
use app\modules\backend\services\report\ClientDebtReportService;
use app\modules\backend\services\report\SupplierBalanceReportService;
use app\common\models\Product;
use app\common\models\Material;
use app\common\models\MaterialCategory;
use app\modules\backend\models\Region;
use yii\helpers\ArrayHelper;
use yii\db\Query;
use Yii;
use app\common\models\Cashbox;
use app\modules\backend\services\report\CashboxReportService;

class ReportController extends Controller
{
    /**
     * Главная страница отчетов с табами
     *
     * @return string
     */
    public function actionIndex()
    {
        return $this->render('index');
    }

    /**
     * Отчет по продажам
     *
     * @return string
     */
    public function actionSales()
    {
        $request = Yii::$app->request;
        $startDate = $request->get('start_date', date('Y-m-d'));
        $endDate = $request->get('end_date', date('Y-m-d'));
        $productId = $request->get('product_id');
        $status = $request->get('status');
        
        $reportService = new ReportService();
        $reportData = $reportService->getSalesReport($startDate, $endDate, $productId, $status);
        
        return $this->render('sales', [
            'reportData' => $reportData,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'productId' => $productId,
            'status' => $status,
        ]);
    }

    /**
     * Отчет по финансам (расходы/доходы)
     *
     * @return string
     */
    public function actionFinance()
    {
        $request = Yii::$app->request;
        $startDate = $request->get('start_date', date('Y-m-d')); // Сегодняшний день
        $endDate = $request->get('end_date', date('Y-m-d'));     // Сегодняшний день

        $reportService = new FinanceReportService();
        $reportData = $reportService->getFinanceReport($startDate, $endDate);

        return $this->render('finance', [
            'reportData' => $reportData,
            'startDate' => $startDate,
            'endDate' => $endDate,
        ]);
    }

    /**
     * Отчет по остаткам продукции
     *
     * @return string
     */
    public function actionProductStock()
    {
        $request = Yii::$app->request;
        $productId = $request->get('product_id');

        $reportService = new ProductStockReportService();
        $reportData = $reportService->getProductStockReport($productId);

        $products = ArrayHelper::map(Product::find()->where(['IS', 'deleted_at', null])->all(), 'id', 'name');

        return $this->render('product-stock', [
            'reportData' => $reportData,
            'productId' => $productId,
            'products' => $products,
        ]);
    }    /**
     * Отчет по остаткам сырья
     *
     * @return string
     */
    public function actionMaterialStock()
    {
      

        try {
            $reportService = new MaterialStockReportService();
            $reportData = $reportService->getMaterialStockReport();

            $materials = ArrayHelper::map(Material::find()->where(['IS', 'deleted_at', null])->all(), 'id', 'name');

            
            return $this->render('material-stock', [
                'reportData' => $reportData,
                'materials' => $materials
            ]);
        } catch (\Exception $e) {
            return $this->render('material-stock', [
                'reportData' => ['items' => []],
                'materials' => [],
            ]);
        }
    }

    /**
     * Отчет по производству - сегодняшние выпущенные продукты
     *
     * @return string
     */
    public function actionProduction()
    {
        $request = Yii::$app->request;
        $date = $request->get('date', date('Y-m-d')); // Только сегодняшний день
        $productId = $request->get('product_id');

        $reportService = new ProductionReportService();
        $reportData = $reportService->getTodayProductionReport($date, $productId);

        $products = ArrayHelper::map(Product::find()->where(['IS', 'deleted_at', null])->all(), 'id', 'name');

        return $this->render('production', [
            'reportData' => $reportData,
            'date' => $date,
            'productId' => $productId,
            'products' => $products,
        ]);
    }

    /**
     * Отчет по бесплатным продуктам
     *
     * @return string
     */
    public function actionFreeProducts()
    {
        $request = Yii::$app->request;
        $startDate = $request->get('start_date', date('Y-m-d'));
        $endDate = $request->get('end_date', date('Y-m-d'));
        $productId = $request->get('product_id');
        
        $reportService = new ReportService();
        $reportData = $reportService->getFreeProductsReport($startDate, $endDate, $productId);
        
        $products = ArrayHelper::map(Product::find()->where(['IS', 'deleted_at', null])->all(), 'id', 'name');
        
        return $this->render('free-products', [
            'reportData' => $reportData,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'productId' => $productId,
            'products' => $products,
        ]);
    }

    /**
     * Отчет по задолженностям клиентов
     *
     * @return string
     */
    public function actionDebt()
    {
        $request = Yii::$app->request;
        // Отчет формируем только по должникам
        $type = 'debtors';
        $regionId = $request->get('region_id');

        $reportService = new ClientDebtReportService();
        $reportData = $reportService->getClientDebtReport($type, $regionId);

        $regions = ArrayHelper::map(
            Region::find()->where(['IS', 'deleted_at', null])->all(), 
            'id', 
            'name'
        );

        return $this->render('debt', [
            'reportData' => $reportData,
            'type' => $type,
            'regionId' => $regionId,
            'regions' => $regions,
        ]);
    }

    /**
     * Отчет по балансам поставщиков
     * @return string
     */
    public function actionSupplierBalance()
    {
        $reportService = new SupplierBalanceReportService();
        $reportData = $reportService->getSupplierBalanceReport();

        return $this->render('supplier-balance', [
            'reportData' => $reportData,
        ]);
    }

    /**
     * Отчет по кассе
     *
     * @return string
     */
    public function actionCashbox()
    {
        $request    = Yii::$app->request;
        $startDate  = $request->get('start_date', date('Y-m-d'));
        $endDate    = $request->get('end_date', date('Y-m-d'));
        $cashboxId  = $request->get('cashbox_id');

        $reportService = new CashboxReportService();
        $reportData    = $reportService->getCashboxReport($startDate, $endDate, $cashboxId);

        $cashboxes = ArrayHelper::map(
            Cashbox::find()->where(['IS', 'deleted_at', null])->all(),
            'id',
            'title'
        );

        return $this->render('cashbox', [
            'reportData' => $reportData,
            'startDate'  => $startDate,
            'endDate'    => $endDate,
            'cashboxId'  => $cashboxId,
            'cashboxes'  => $cashboxes,
        ]);
    }

    /**
     * Отчет охранника
     *
     * @return string
     */
    public function actionSecurity()
    {
        $request = Yii::$app->request;
        $startDate = $request->get('start_date', date('Y-m-d'));
        $endDate = $request->get('end_date', date('Y-m-d'));
        $regionId = $request->get('region_id');
        $status = $request->get('status'); // pending, accepted
        
        // Получаем данные из security_records
        $query = new Query();
        $query->select([
            'sr.id',
            'sr.car_number',
            'sr.driver_full_name',
            'sr.description',
            'sr.created_at',
            'sr.accepted_at',
            'sr.image',
            'u1.username as add_user_name',
            'u2.username as accepted_user_name',
            'r.name as region_name',
            's.id as invoice_id',
            's.total_sum as invoice_total',
            's.created_at as invoice_date'
        ])
        ->from(['sr' => 'security_records'])
        ->leftJoin(['u1' => 'users'], 'sr.add_user_id = u1.id')
        ->leftJoin(['u2' => 'users'], 'sr.accepted_user_id = u2.id')
        ->leftJoin(['r' => 'region'], 'sr.region_id = r.id')
        ->leftJoin(['s' => 'sales'], 's.deleted_at IS NULL AND s.id = (
            SELECT MAX(s2.id) FROM sales s2 
            WHERE s2.deleted_at IS NULL
            AND s2.driver IS NOT NULL
            AND sr.driver_full_name IS NOT NULL
            AND LENGTH(TRIM(sr.driver_full_name)) >= 3
            AND LENGTH(TRIM(s2.driver)) >= 3
            AND (
                UPPER(TRIM(sr.driver_full_name)) = UPPER(TRIM(s2.driver))
                OR (
                    LENGTH(TRIM(sr.driver_full_name)) >= 5 
                    AND UPPER(TRIM(s2.driver)) LIKE \'%\' || UPPER(TRIM(sr.driver_full_name)) || \'%\'
                )
                OR (
                    LENGTH(TRIM(s2.driver)) >= 5 
                    AND UPPER(TRIM(sr.driver_full_name)) LIKE \'%\' || UPPER(TRIM(s2.driver)) || \'%\'
                )
            )
        )')
        ->where(['IS', 'sr.deleted_at', null]);

        // Фильтры по дате
        if ($startDate) {
            $query->andWhere(['>=', 'DATE(sr.created_at)', $startDate]);
        }
        if ($endDate) {
            $query->andWhere(['<=', 'DATE(sr.created_at)', $endDate]);
        }

        // Фильтр по региону
        if ($regionId) {
            $query->andWhere(['sr.region_id' => $regionId]);
        }

        // Фильтр по статусу
        if ($status === 'pending') {
            $query->andWhere(['IS', 'sr.accepted_at', null]);
        } elseif ($status === 'accepted') {
            $query->andWhere(['IS NOT', 'sr.accepted_at', null]);
        }

        $query->orderBy(['sr.created_at' => SORT_DESC]);

        $reportData = [
            'items' => $query->all(),
            'total_count' => $query->count(),
            'pending_count' => (new Query())
                ->from('security_records')
                ->where(['IS', 'deleted_at', null])
                ->andWhere(['IS', 'accepted_at', null])
                ->count(),
            'accepted_count' => (new Query())
                ->from('security_records')
                ->where(['IS', 'deleted_at', null])
                ->andWhere(['IS NOT', 'accepted_at', null])
                ->count()
        ];

        $regions = ArrayHelper::map(
            Region::find()->where(['IS', 'deleted_at', null])->all(),
            'id',
            'name'
        );

        return $this->render('security', [
            'reportData' => $reportData,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'regionId' => $regionId,
            'status' => $status,
            'regions' => $regions,
        ]);
    }
}
