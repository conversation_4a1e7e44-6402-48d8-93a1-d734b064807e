<?php

namespace app\modules\backend\services\report;

use yii\db\Query;
use yii\helpers\ArrayHelper;
use app\common\models\PaymentType;

/**
 * Сервис для формирования отчетов по финансам (расходы/доходы)
 */
class FinanceReportService
{
    /**
     * Получить отчет по финансам
     * 
     * @param string|null $startDate Начальная дата в формате Y-m-d
     * @param string|null $endDate Конечная дата в формате Y-m-d
     * @return array Массив с данными отчета
     */
    public function getFinanceReport($startDate = null, $endDate = null)
    {
        $incomeData = $this->getIncomeData($startDate, $endDate);
        $expenseData = $this->getExpenseData($startDate, $endDate);
        
        return [
            'income' => $incomeData,
            'expenses' => $expenseData,
            'summary' => [
                'totalIncome' => $incomeData['total'] ?? 0,
                'totalExpenses' => $expenseData['total'] ?? 0,
                'profit' => ($incomeData['total'] ?? 0) - ($expenseData['total'] ?? 0),
            ]
        ];
    }
    
    /**
     * Получить данные по доходам
     * 
     * @param string|null $startDate
     * @param string|null $endDate
     * @return array
     */
    protected function getIncomeData($startDate = null, $endDate = null)
    {
        $query = new Query();
        
        // Изменено: теперь выбираем платежи клиентов
        $query->select([
                'cp.id',
                'cp.created_at',
                'cp.summa as amount',
                'cp.type as payment_type',
                'c.full_name as client_name',
                'u.username as cashier_name',
                'cb.title as cashbox_name',
                'cur.name as currency'
            ])
            ->from(['cp' => 'client_payments'])
            ->leftJoin(['c' => 'client'], 'cp.client_id = c.id')
            ->leftJoin(['u' => 'users'], 'cp.add_user_id = u.id')
            ->leftJoin(['cb' => 'cashbox'], 'cp.cashbox_id = cb.id')
            ->leftJoin(['cur' => 'currency'], 'cb.currency_id = cur.id')
            ->where(['IS', 'cp.deleted_at', null]);
            
        if ($startDate) {
            $query->andWhere(['>=', 'cp.created_at', $startDate . ' 00:00:00']);
        }
        
        if ($endDate) {
            $query->andWhere(['<=', 'cp.created_at', $endDate . ' 23:59:59']);
        }
        
        $query->orderBy(['cp.created_at' => SORT_DESC]);
        
        $items = $query->all();
        
        // Преобразуем тип платежа в читаемый формат
        foreach ($items as &$row) {
            if (isset($row['payment_type'])) {
                $row['payment_type'] = PaymentType::getTypeDescription((int)$row['payment_type']);
            }
        }
        unset($row);
        
        $total = array_sum(ArrayHelper::getColumn($items, 'amount'));
        
        return [
            'items' => $items,
            'total' => $total
        ];
    }
    
    /**
     * Получить данные по расходам
     * 
     * @param string|null $startDate
     * @param string|null $endDate
     * @return array
     */
    protected function getExpenseData($startDate = null, $endDate = null)
    {
        $query = new Query();
        $query->select([
                'e.id',
                'e.created_at',
                'COALESCE(sp.amount, e.summa) as amount',
                'et.name as expense_type',
                'e.description',
                'e.payment_type',
                'cur.name as currency',
                'u.username as created_by'
            ])
            ->from(['e' => 'expenses'])
            ->leftJoin(['sp' => 'supplier_payments'], 'sp.expense_id = e.id')
            ->leftJoin(['s' => 'supplier'], 's.id = sp.supplier_id')
            ->leftJoin(['cur' => 'currency'], 'cur.id = s.currency_id')
            ->leftJoin(['et' => 'expenses_type'], 'e.expense_type_id = et.id')
            ->leftJoin(['u' => 'users'], 'e.add_user_id = u.id')
            ->where(['IS', 'e.deleted_at', null]);
            
        if ($startDate) {
            $query->andWhere(['>=', 'e.created_at', $startDate . ' 00:00:00']);
        }
        
        if ($endDate) {
            $query->andWhere(['<=', 'e.created_at', $endDate . ' 23:59:59']);
        }
        
        $query->orderBy(['e.created_at' => SORT_DESC]);
        
        $items = $query->all();
        $totalSom = $totalUsd = 0;
        foreach ($items as $item) {
            $cur = mb_strtoupper($item['currency'] ?? 'UZS');
            if (strpos($cur, 'USD') !== false || strpos($cur, 'DOLLAR') !== false) {
                $totalUsd += (float)$item['amount'];
            } else {
                $totalSom += (float)$item['amount']; // UZS/Сӯм/Сум и т.д.
            }
        }
        foreach ($items as &$row) {
            if (isset($row['payment_type'])) {
                $row['payment_type'] = PaymentType::getTypeDescription((int)$row['payment_type']);
            }
        }
        unset($row);
        $total = array_sum(ArrayHelper::getColumn($items, 'amount'));
        
        return [
            'items'      => $items,
            'total_som'  => $totalSom,
            'total_usd'  => $totalUsd,
            'total'      => $totalSom + $totalUsd
        ];
    }
}