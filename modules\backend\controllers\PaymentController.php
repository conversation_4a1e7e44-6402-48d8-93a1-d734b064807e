<?php
namespace app\modules\backend\controllers;

use app\common\models\CashboxDetail;
use app\common\models\Client;
use Yii;
use yii\web\Response;
use app\common\models\ClientPayments;
use app\common\models\Tracking;
use app\common\models\ClientBalance;
use app\common\models\ClientBalanceHistory;
use app\common\models\Cashbox;

class PaymentController extends BaseController
{
    public function actionIndex()
    {
        $query = ClientPayments::find()
            ->select([
                'client_payments.*',
                'client.full_name as client_name',
                'users.full_name as user_name',
            ])
            ->leftJoin('client', 'client_payments.client_id = client.id')
            ->leftJoin('users', 'client_payments.add_user_id = users.id')
            ->orderBy(['client_payments.created_at' => SORT_DESC]);

        $result = $query->asArray()->all();
        $clients = Client::find()->where(['deleted_at' => null])->all();

        return $this->render('index', [
            'result' => $result,
            'clients' => $clients
        ]);
    }

    public function actionSearch()
{
    $startDate = Yii::$app->request->post('start_date');
    $endDate = Yii::$app->request->post('end_date');
    $clientId = Yii::$app->request->post('client_id');

    $query = ClientPayments::find()
        ->select([
            'client_payments.*',
            'client.full_name as client_name',
            'users.full_name as user_name',
        ])
        ->leftJoin('client', 'client_payments.client_id = client.id')
        ->leftJoin('users', 'client_payments.add_user_id = users.id');

    if (!empty($startDate) && !empty($endDate)) {
        $query->andWhere(['between', 'DATE(client_payments.created_at)', $startDate, $endDate]);
    }

    if (!empty($clientId)) {
        $query->andWhere(['client_payments.client_id' => $clientId]);
    }

    $query->orderBy(['client_payments.created_at' => SORT_DESC]);
    $result = $query->asArray()->all();

    foreach ($result as &$row) {
        $tracking = Tracking::find()
            ->where(['process_id' => $row['id']])
            ->andWhere(['progress_type' => Tracking::PAY_FOR_CLIENT])
            ->andWhere(['is', 'accepted_at', null])
            ->andWhere(['is', 'deleted_at', null])
            ->one();
        
        $row['can_delete'] = ($tracking !== null);
    }

    if (Yii::$app->request->isAjax) {
        return $this->renderAjax('_index', [
            'result' => $result
        ]);
    }

    // Если не AJAX, возвращаем JSON
    return $this->asJson([
        'status' => 'success',
        'data' => $result
    ]);
}


    public function actionDelete()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if(Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $id = $data['ClientPayments']['id'];
            $tracking = Tracking::find()
                ->where(['process_id' => $id])
                ->andWhere(['progress_type' => Tracking::PAY_FOR_CLIENT])
                ->andWhere(['is', 'accepted_at', null])
                ->andWhere(['is', 'deleted_at', null])
                ->one();

            if (!$tracking) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Cannot delete confirmed payment')
                ];
            }

            $model = ClientPayments::findOne($id);

            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Record not found')
                ];
            }

            $transaction = Yii::$app->db->beginTransaction();
            try {
                $clientBalance = ClientBalance::findOne(['client_id' => $model->client_id]);
                if ($clientBalance) {
                    $oldAmount = $clientBalance->amount;
                    $clientBalance->amount -= $model->summa;
                    
                    if (!$clientBalance->save()) {
                        throw new \Exception(Yii::t('app', 'Error updating client balance'));
                    }

                    $balanceHistory = new ClientBalanceHistory();
                    $balanceHistory->client_id = $model->client_id;
                    $balanceHistory->amount = $clientBalance->amount;
                    $balanceHistory->old_amount = $oldAmount;
                    $balanceHistory->type = $model->type;
                    
                    if (!$balanceHistory->save()) {
                        throw new \Exception(Yii::t('app', 'Error saving balance history'));
                    }
                }

                // Используем валюту по умолчанию (ID = 2) для платежей клиентов
                $cashbox = Cashbox::findByPaymentTypeAndCurrency($model->type, 2);
                if (!$cashbox) {
                    throw new \Exception(Yii::t('app', 'Cashbox not found'));
                }

                $cashboxDetail = new CashboxDetail();
                $cashboxDetail->cashbox_id = $cashbox->id;
                $cashboxDetail->add_user_id = Yii::$app->user->id;
                $cashboxDetail->type = CashboxDetail::TYPE_OUT; 
                $cashboxDetail->amount = $model->summa; 
                $cashboxDetail->created_at = date('Y-m-d H:i:s');

                if (!$cashboxDetail->save()) {
                    throw new \Exception(Yii::t('app', 'Error saving cashbox detail: ') . json_encode($cashboxDetail->getErrors(), JSON_UNESCAPED_UNICODE));
                }

                $cashbox->balance -= $model->summa; 
                if (!$cashbox->save()) {
                    throw new \Exception(Yii::t('app', 'Error updating cashbox balance'));
                }

                $tracking->deleted_at = date('Y-m-d H:i:s');
                if (!$tracking->save()) {
                    throw new \Exception(Yii::t('app', 'Error updating tracking'));
                }

                $model->deleted_at = date('Y-m-d H:i:s');
                if (!$model->save()) {
                    throw new \Exception(Yii::t('app', 'Error deleting payment'));
                }

                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'record_successfully_deleted')
                ];

            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        } else if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = ClientPayments::findOne($id);
            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Record not found')
                ];
            }
            return [
                'status'=> 'success',
                'content' => $this->renderPartial('delete', ['model'=> $model]),
            ];
        }

        return [
            'status' => 'error',
            'message' => Yii::t('app', 'Error deleting record')
        ];
    }
}
