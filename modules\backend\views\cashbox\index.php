<?php

use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\grid\GridView;
use yii\web\View;
use app\assets\DataTablesAsset;
use app\common\models\Cashbox;

DataTablesAsset::register($this);

$this->title = Yii::t("app", "cashbox_section");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
?>

<style>
    .modal-dialog {
        max-width: 695px !important;
    }
</style>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>
        <div class="col-md-6 text-right">
            <?php if (Yii::$app->user->can('admin')): ?>
                <button type="button" class="btn btn-success mr-2 cashbox-create" data-toggle="modal" data-target="#ideal-mini-modal">
                    <i class="fas fa-plus"></i> <?= Yii::t("app", "create_cashbox") ?>
                </button>
                <a href="#" class="btn btn-primary cashbox-transfer" data-toggle="modal" data-target="#ideal-mini-modal">
                    <i class="fas fa-exchange-alt"></i> <?= Yii::t("app", "add_transfer") ?>
                </a>
            <?php endif ?>
        </div>
    </div>

    <?php Pjax::begin(['id' => 'cashbox-grid-pjax']); ?>
    <?php if($dataProvider->models): ?>
        <div>
            <table id="cashbox-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <th>ID</th>
                    <th><?= Yii::t("app", "cashbox_name") ?></th>
                    <th><?= Yii::t("app", "currency") ?></th>
                    <th><?= Yii::t("app", "payments_type") ?></th>
                    <th><?= Yii::t("app", "balance") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </thead>
                <tbody>
                <?php foreach ($dataProvider->models as $model): ?>
                    <tr>
                        <td><?= Html::encode($model->id) ?></td>
                        <td>
                            <strong><?= Html::encode($model->title) ?></strong>
                            <br>
                            <small class="text-muted">
                                Создана: <?= Yii::$app->formatter->asDatetime($model->created_at) ?>
                            </small>
                        </td>
                        <td>
                            <span class="badge badge-info">
                                <?= $model->currency ? Html::encode($model->currency->name) : 'Не указана' ?>
                            </span>
                        </td>
                        <td>
                            <small class="text-info">
                                <?= $model->getPaymentTypesString() ?>
                            </small>
                        </td>
                        <td>
                            <span class="badge <?= $model->balance >= 0 ? 'badge-success' : 'badge-danger' ?>">
                                <?= number_format($model->balance, 2) ?>
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="<?= Yii::$app->urlManager->createUrl(['/backend/cashbox/detail', 'id' => $model->id]) ?>" 
                                   class="btn btn-info btn-sm" title="Просмотр операций">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <?php if (Yii::$app->user->can('admin')): ?>
                                    <button type="button" class="btn btn-warning btn-sm cashbox-update" 
                                            data-id="<?= $model->id ?>" data-toggle="modal" data-target="#ideal-mini-modal" title="Редактировать">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm cashbox-delete" 
                                            data-id="<?= $model->id ?>" data-toggle="modal" data-target="#ideal-mini-modal" title="Удалить">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="alert alert-info text-center">
            <i class="fas fa-info-circle"></i>
            <?= Yii::t('app', 'no_data_available') ?>
        </div>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>



<div id="one" data-text="<?= Yii::t("app", "balance_transfer") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "create_cashbox") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "update_cashbox") ?>"></div>
<div id="four" data-text="<?= Yii::t("app", "delete_cashbox") ?>"></div>

<?php
$js = <<<JS
(function($) {
    var one = $('#one').attr('data-text');
    var two = $('#two').attr('data-text');
    var three = $('#three').attr('data-text');
    var four = $('#four').attr('data-text');

    var searchLabel = "{$searchLabel}";
    var lengthMenuLabel = "{$lengthMenuLabel}";
    var zeroRecordsLabel = "{$zeroRecordsLabel}";
    var infoLabel = "{$infoLabel}";
    var infoEmptyLabel = "{$infoEmptyLabel}";
    var infoFilteredLabel = "{$infoFilteredLabel}";

    function initializeDataTable() {
        if ($('#cashbox-grid-view').length && $('#cashbox-grid-view tbody tr').length > 0) {
            if ($.fn.DataTable.isDataTable('#cashbox-grid-view')) {
                $('#cashbox-grid-view').DataTable().destroy();
            }

            $('#cashbox-grid-view').DataTable({
                "language": {
                    "search": searchLabel,
                    "lengthMenu": lengthMenuLabel,
                    "zeroRecords": zeroRecordsLabel,
                    "info": infoLabel,
                    "infoEmpty": infoEmptyLabel,
                    "infoFiltered": infoFilteredLabel
                },
                "pageLength": 50,
                "order": [[0, 'desc']],
                "columnDefs": [
                    {
                        "targets": [5],
                        "orderable": false
                    }
                ]
            });
        }
    }

    function initializeAll() {
        initializeDataTable();
        initializeSelect2();
        initializeCashboxTransfer();
        initializeCashboxCRUD();
    }

    // Initialize everything on first load
    initializeAll();

    // Re-initialize after PJAX reloads
    $(document).on('pjax:success', function() {
        initializeAll();
    });

    function initializeSelect2() {
        $('.select2').select2({
            width: '100%',
            language: {
                noResults: function() {
                    return "Натижа топилмади";
                }
            }
        });
    }

    // CRUD операции для касс
    function initializeCashboxCRUD() {
        // Создание кассы
        $(document).off('click.cashbox-create').on('click.cashbox-create', '.cashbox-create', function() {
            $.ajax({
                url: '/backend/cashbox/create',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#ideal-mini-modal .modal-title').html(two);
                        $('#ideal-mini-modal .modal-body').html(response.content);
                        $('#ideal-mini-modal .mini-button').removeClass().addClass('btn btn-success mini-button cashbox-create-button');
                        $('#ideal-mini-modal .mini-button').html(two);
                        initializeSelect2();
                    } else {
                        toastr.error(response.message || 'Ошибка загрузки формы');
                    }
                },
                error: function() {
                    toastr.error('Ошибка при загрузке формы создания');
                }
            });
        });

        // Редактирование кассы
        $(document).off('click.cashbox-update').on('click.cashbox-update', '.cashbox-update', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/cashbox/update',
                type: 'GET',
                data: { id: id },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#ideal-mini-modal .modal-title').html(three);
                        $('#ideal-mini-modal .modal-body').html(response.content);
                        $('#ideal-mini-modal .mini-button').removeClass().addClass('btn btn-primary mini-button cashbox-update-button');
                        $('#ideal-mini-modal .mini-button').html(three);
                        initializeSelect2();
                    } else {
                        toastr.error(response.message || 'Ошибка загрузки формы');
                    }
                },
                error: function() {
                    toastr.error('Ошибка при загрузке формы редактирования');
                }
            });
        });

        // Удаление кассы
        $(document).off('click.cashbox-delete').on('click.cashbox-delete', '.cashbox-delete', function() {
            var id = $(this).data('id');
            $.ajax({
                url: '/backend/cashbox/delete',
                type: 'GET',
                data: { id: id },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        $('#ideal-mini-modal .modal-title').html(four);
                        $('#ideal-mini-modal .modal-body').html(response.content);
                        $('#ideal-mini-modal .mini-button').removeClass().addClass('btn btn-danger mini-button cashbox-delete-button');
                        $('#ideal-mini-modal .mini-button').html(four);
                    } else {
                        toastr.error(response.message || 'Ошибка загрузки формы');
                    }
                },
                error: function() {
                    toastr.error('Ошибка при загрузке формы удаления');
                }
            });
        });

        // Обработчики кнопок в модальном окне
        $(document).off('click.cashbox-create-button').on('click.cashbox-create-button', '.cashbox-create-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#cashbox-create-form').serialize();
                $.ajax({
                    url: '/backend/cashbox/create',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({container: '#cashbox-grid-pjax'});
                            initializeDataTable();
                            toastr.success(response.message);
                        } else if (response && response.status === 'error') {
                            button.prop('disabled', false);
                            if (response.errors) {
                                var errorMsg = '';
                                if (typeof response.errors === 'object') {
                                    Object.keys(response.errors).forEach(function(key) {
                                        errorMsg += response.errors[key] + '<br>';
                                    });
                                } else {
                                    errorMsg = response.errors;
                                }
                                toastr.error(errorMsg);
                            } else {
                                toastr.error(response.message || 'Произошла ошибка');
                            }
                        }
                    },
                    error: function() {
                        button.prop('disabled', false);
                        toastr.error('Произошла ошибка при отправке запроса');
                    }
                });
            }
        });

        $(document).off('click.cashbox-update-button').on('click.cashbox-update-button', '.cashbox-update-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#cashbox-update-form').serialize();
                $.ajax({
                    url: '/backend/cashbox/update',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({container: '#cashbox-grid-pjax'});
                            initializeDataTable();
                            toastr.success(response.message);
                        } else if (response && response.status === 'error') {
                            button.prop('disabled', false);
                            if (response.errors) {
                                var errorMsg = '';
                                if (typeof response.errors === 'object') {
                                    Object.keys(response.errors).forEach(function(key) {
                                        errorMsg += response.errors[key] + '<br>';
                                    });
                                } else {
                                    errorMsg = response.errors;
                                }
                                toastr.error(errorMsg);
                            } else {
                                toastr.error(response.message || 'Произошла ошибка');
                            }
                        }
                    },
                    error: function() {
                        button.prop('disabled', false);
                        toastr.error('Произошла ошибка при отправке запроса');
                    }
                });
            }
        });

        $(document).off('click.cashbox-delete-button').on('click.cashbox-delete-button', '.cashbox-delete-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#cashbox-delete-form').serialize();
                $.ajax({
                    url: '/backend/cashbox/delete',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({container: '#cashbox-grid-pjax'});
                            initializeDataTable();
                            toastr.success(response.message);
                        } else if (response && response.status === 'error') {
                            button.prop('disabled', false);
                            toastr.error(response.message || 'Произошла ошибка при удалении');
                        }
                    },
                    error: function() {
                        button.prop('disabled', false);
                        toastr.error('Произошла ошибка при отправке запроса');
                    }
                });
            }
        });
    }

    function initializeCashboxTransfer() {
        $(document).off('click.cashbox-transfer').on('click.cashbox-transfer', '.cashbox-transfer', function() {
            $.ajax({
                url: '/backend/cashbox/transfer',
                dataType: 'json',
                type: 'GET',
                success: function(response) {
                    $('#ideal-mini-modal .modal-title').html(one);
                    $('#ideal-mini-modal .modal-body').html(response.content);
                    $('#ideal-mini-modal .mini-button').addClass("cashbox-transfer-button");
                    initializeSelect2();
                },
                error: function(xhr, textStatus, errorThrown) {
                    console.error('AJAX Error:', xhr.statusText, errorThrown);
                }
            });
        });

        $(document).off('click.cashbox-transfer-button').on('click.cashbox-transfer-button', '.cashbox-transfer-button', function() {
            var button = $(this);
            if (!button.prop('disabled')) {
                button.prop('disabled', true);
                var formData = $('#transfer-form').serialize();
                $.ajax({
                    url: '/backend/cashbox/transfer',
                    dataType: 'json',
                    type: 'POST',
                    data: formData,
                    success: function(response) {
                        if (response && response.status === 'success') {
                            button.prop('disabled', false);
                            $('.close').trigger('click');
                            $.pjax.reload({container: '#cashbox-grid-pjax'});
                            initializeDataTable();
                            toastr.success(response.message);
                        } else if (response && response.status === 'error') {
                            button.prop('disabled', false);
                            $('.help-block').html('');
                            $.each(response.errors, function(field, errors) {
                                $('#' + field + '-error').html(errors.join('<br>'));
                            });
                        }
                    },
                    error: function(xhr, textStatus, errorThrown) {
                        button.prop('disabled', false);
                        console.error('AJAX Error:', xhr.statusText, errorThrown);
                    }
                });
            }
        });

        $(document).off('keypress.cashbox-transfer-button').on('keypress.cashbox-transfer-button', '#transfer-form', function(e) {
            if (e.which === 13) {
                e.preventDefault();
                $('.cashbox-transfer-button').trigger('click');
            }
        });
    }

})(jQuery);
JS;
$this->registerJs($js, View::POS_END);
?>
