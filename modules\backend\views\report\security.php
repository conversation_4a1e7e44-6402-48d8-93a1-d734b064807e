<?php

use yii\helpers\Html;
use yii\helpers\Url;

/* @var $this yii\web\View */
/* @var $reportData array */
/* @var $startDate string */
/* @var $endDate string */
/* @var $regionId int|null */
/* @var $status string|null */
/* @var $regions array */

$this->title = Yii::t('app', 'security_report');
?>

<style>
    .text-right{ text-align:right; }
    .status-pending{ color:#ffc107; font-weight:bold; }
    .status-accepted{ color:#28a745; font-weight:bold; }
    .no-wrap{ white-space:nowrap; }
    .table-responsive{ overflow-x:auto; }
    .badge-pending{ background-color:#ffc107; color:#212529; }
    .badge-accepted{ background-color:#28a745; color:#fff; }
</style>

<div class="row align-items-center mb-3">
    <div class="col-md-6">
        <h3 class="mb-0"><?= Yii::t('app', 'security_report') ?></h3>
    </div>
    <div class="col-md-6 d-flex justify-content-end">
        <?= Html::beginForm(['security'], 'get', ['class' => 'd-inline-flex align-items-center']) ?>
            <?= Html::dropDownList('region_id', $regionId, ['' => Yii::t('app', 'regions')] + $regions, [
                'class' => 'form-control mr-2',
                'style' => 'width:180px;'
            ]) ?>
            <?= Html::dropDownList('status', $status, [
                '' => Yii::t('app', 'status'),
                'pending' => Yii::t('app', 'pending'),
                'accepted' => Yii::t('app', 'accepted')
            ], [
                'class' => 'form-control mr-2',
                'style' => 'width:140px;'
            ]) ?>
            <?= Html::input('date', 'start_date', $startDate, ['class' => 'form-control mr-2', 'style' => 'width: 160px;', 'placeholder' => 'С']) ?>
            <?= Html::input('date', 'end_date', $endDate, ['class' => 'form-control mr-2', 'style' => 'width: 160px;', 'placeholder' => 'По']) ?>
            <?= Html::submitButton('<i class="fas fa-search"></i> ' . Yii::t('app', 'search'), ['class' => 'btn btn-primary']) ?>
        <?= Html::endForm() ?>
    </div>
</div>



<?php $items = $reportData['items'] ?? []; ?>
<?php if (!empty($items)): ?>
    <div class="card">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-bordered table-striped table-hover mb-0">
                    <thead class="thead-light">
                        <tr>
                            <th>ID</th>
                            <th><?= Yii::t('app', 'car_number') ?></th>
                            <th><?= Yii::t('app', 'driver_name') ?></th>
                            <th><?= Yii::t('app', 'invoice_id') ?></th>
                            <th><?= Yii::t('app', 'description') ?></th>
                            <th><?= Yii::t('app', 'region') ?></th>
                            <th><?= Yii::t('app', 'created_by') ?></th>
                            <th><?= Yii::t('app', 'created_at') ?></th>
                            <th><?= Yii::t('app', 'accepted_by') ?></th>
                            <th><?= Yii::t('app', 'accepted_at') ?></th>
                            <th class="text-center"><?= Yii::t('app', 'status') ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($items as $row): ?>
                            <?php $isAccepted = !empty($row['accepted_at']); ?>
                            <tr>
                                <td><?= Html::encode($row['id']) ?></td>
                                <td><?= Html::encode($row['car_number']) ?></td>
                                <td><?= Html::encode($row['driver_full_name']) ?></td>
                                <td>
                                    <?php if (!empty($row['invoice_id'])): ?>
                                        <a href="<?= Url::to(['/backend/invoice/view', 'id' => $row['invoice_id']]) ?>" 
                                           class="text-primary font-weight-bold" 
                                           title="<?= Yii::t('app', 'view_invoice') ?>">
                                            #<?= Html::encode($row['invoice_id']) ?>
                                        </a>
                                        <br>
                                        <small class="text-muted">
                                            <?= Yii::$app->formatter->asDecimal($row['invoice_total'], 0) ?> <?= Yii::t('app', 'sum') ?>
                                        </small>
                                    <?php else: ?>
                                        <span class="text-muted">-</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= Html::encode($row['description'] ?: '-') ?></td>
                                <td><?= Html::encode($row['region_name'] ?: '-') ?></td>
                                <td><?= Html::encode($row['add_user_name'] ?: '-') ?></td>
                                <td class="no-wrap"><?= Yii::$app->formatter->asDatetime($row['created_at'], 'php:d.m.Y H:i') ?></td>
                                <td><?= Html::encode($row['accepted_user_name'] ?: '-') ?></td>
                                <td class="no-wrap">
                                    <?= $row['accepted_at'] ? Yii::$app->formatter->asDatetime($row['accepted_at'], 'php:d.m.Y H:i') : '-' ?>
                                </td>
                                <td class="text-center">
                                    <?php if ($isAccepted): ?>
                                        <span class="badge badge-accepted"><?= Yii::t('app', 'accepted') ?></span>
                                    <?php else: ?>
                                        <span class="badge badge-pending"><?= Yii::t('app', 'pending') ?></span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
<?php else: ?>
    <div class="alert alert-info text-center">
        <i class="fas fa-info-circle mr-2"></i>
        <?= Yii::t('app', 'no_data_found') ?>
    </div>
<?php endif; ?>

<script>
    // валидация года в date input
    document.querySelectorAll('input[type="date"]').forEach(input => {
        input.addEventListener('input', function(e){
            let val = e.target.value;
            if (val) {
                let parts = val.split('-');
                if(parts[0].length > 4){ parts[0] = parts[0].slice(0,4); }
                if(parseInt(parts[0]) > 9999){ parts[0] = '9999'; }
                e.target.value = parts.join('-');
            }
        });
    });
</script> 