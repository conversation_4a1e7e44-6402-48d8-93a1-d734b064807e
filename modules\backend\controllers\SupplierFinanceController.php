<?php

namespace app\modules\backend\controllers;

use app\common\models\CashboxDetail;
use app\common\models\Supplier;
use Yii;
use yii\web\Controller;
use app\common\models\SupplierPayments;
use app\common\models\Invoice;
use app\common\models\InvoiceDetail;
use app\common\models\SupplierBalance;
use app\common\models\SupplierBalanceHistory;
use app\common\models\Tracking;
use app\modules\backend\models\Expenses;
use app\common\models\Cashbox;
use yii\web\Response;
use app\modules\backend\models\IncomeMaterialUpdateForm;
use app\common\models\MaterialStorage;
use app\common\models\MaterialStorageHistory;
use app\common\models\Material;
use app\common\models\ActionLogger;
use yii\helpers\ArrayHelper;

class SupplierFinanceController extends Controller
{
    public function actionIndex()
    {
        $materials = Invoice::find()
            ->select([
                'invoice.*',
                'invoice_detail.quantity',
                'invoice_detail.price',
                'supplier.full_name as supplier_name',
                'users.full_name as accepted_user',
                'material.name as material_name',
                'security_records.car_number',
            ])
            ->leftJoin('supplier', 'invoice.supplier_id = supplier.id')
            ->leftJoin('users', 'invoice.accept_user_id = users.id')
            ->leftJoin('invoice_detail', 'invoice.id = invoice_detail.invoice_id')
            ->leftJoin('material', 'invoice_detail.material_id = material.id')
            ->leftJoin('security_records', 'invoice.supplier_id = security_records.id')
            ->asArray()
            ->all();

            $supliers = Supplier::find()->where(['deleted_at' => null])->all();

        return $this->render('index', [
            
            'materials' => $materials,
            'supliers' => $supliers
        ]);
    }

    public function actionPayments() {
        $payments = SupplierPayments::find()
        ->select([
            'supplier_payments.*',
            'supplier.full_name as supplier_name',
            'users.full_name as user_name',
            'expenses_type.name as expense_type_name',
        ])
        ->leftJoin('supplier', 'supplier_payments.supplier_id = supplier.id')
        ->leftJoin('users', 'supplier_payments.add_user_id = users.id')
        ->leftJoin('expenses', 'supplier_payments.expense_id = expenses.id')
        ->leftJoin('expenses_type', 'expenses.expense_type_id = expenses_type.id')
        ->orderBy(['supplier_payments.created_at' => SORT_DESC])
        ->asArray()
        ->all();

        $supliers = Supplier::find()->where(['deleted_at' => null])->all();

        return $this->render('payments', [
            
            'payments' => $payments,
            'supliers' => $supliers
        ]);
    }
     

    public function actionUpdate()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;

        if (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            
            $invoiceDetail = InvoiceDetail::findOne($id);
            if (!$invoiceDetail) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Record not found')
                ];
            }

            $tracking = Tracking::find()
                ->where(['process_id' => $invoiceDetail->invoice_id])
                ->andWhere(['progress_type' => Tracking::TYPE_MATERIAL_INCOME])
                ->andWhere(['is', 'accepted_at', null])
                ->andWhere(['is', 'deleted_at', null])
                ->one();

            if (!$tracking) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Cannot update confirmed material')
                ];
            }

            $model = new IncomeMaterialUpdateForm();
            $model->id = $invoiceDetail->id;
            $model->invoice_id = $invoiceDetail->invoice_id;
            $model->material_id = $invoiceDetail->material_id;
            $model->quantity = $invoiceDetail->quantity;
            $model->price = $invoiceDetail->price;
            $model->description = $invoiceDetail->invoice->description;

            $materials = ArrayHelper::map(Material::find()->all(), 'id', 'name');

            return [
                'status' => 'success',
                'content' => $this->renderPartial('update', [
                    'model' => $model,
                    'materials' => $materials,
                ]),
            ];
        }

        if (Yii::$app->request->isPost) {
            $model = new IncomeMaterialUpdateForm();

            if ($model->load(Yii::$app->request->post()) && $model->validate()) {
                $transaction = Yii::$app->db->beginTransaction();
                try {
                    $invoiceDetail = InvoiceDetail::findOne($model->id);
                    if (!$invoiceDetail) {
                        throw new \Exception(Yii::t('app', 'Record not found'));
                    }

                    // Проверяем tracking как в actionDelete
                    $tracking = Tracking::find()
                        ->where(['process_id' => $invoiceDetail->invoice_id])
                        ->andWhere(['progress_type' => Tracking::TYPE_MATERIAL_INCOME])
                        ->andWhere(['is', 'accepted_at', null])
                        ->andWhere(['is', 'deleted_at', null])
                        ->one();

                    if (!$tracking) {
                        throw new \Exception(Yii::t('app', 'Cannot update confirmed material'));
                    }

                    // Получаем дату накладной
                    $invoice = $invoiceDetail->invoice;
                    $invoiceDate = date('Y-m-d', strtotime($invoice->created_at));

                    // Обработка складского учета для старого материала
                    if ($invoiceDetail->material_id != $model->material_id) {
                        // Уменьшаем количество старого материала
                        $oldStorage = MaterialStorage::findByMaterialAndDate($invoiceDetail->material_id, $invoiceDate);
                        if ($oldStorage) {
                            $oldStorage->quantity -= $invoiceDetail->quantity;
                            if ($oldStorage->quantity < 0) {
                                throw new \Exception("Невозможно обновить накладную. Материал #{$invoiceDetail->material_id} уже был использован.");
                            }
                            if (!$oldStorage->save()) {
                                throw new \Exception('Ошибка обновления склада для старого материала');
                            }

                            // Записываем в историю операцию удаления старого количества
                            $storageHistory = new MaterialStorageHistory();
                            $storageHistory->material_storage_id = $oldStorage->id;
                            $storageHistory->material_id = $invoiceDetail->material_id;
                            $storageHistory->quantity = -$invoiceDetail->quantity;
                            $storageHistory->created_at = date('Y-m-d H:i:s');
                            $storageHistory->add_user_id = Yii::$app->user->id;
                            $storageHistory->type = MaterialStorageHistory::TYPE_OUTCOME;

                            if (!$storageHistory->save()) {
                                throw new \Exception('Ошибка сохранения истории склада при удалении');
                            }
                        }

                        // Увеличиваем количество нового материала
                        $newStorage = MaterialStorage::findByMaterialAndDate($model->material_id, $invoiceDate);
                        if (!$newStorage) {
                            $newStorage = new MaterialStorage();
                            $newStorage->material_id = $model->material_id;
                            $newStorage->quantity = $model->quantity;
                            $newStorage->created_at = $invoice->created_at;
                        } else {
                            $newStorage->quantity += $model->quantity;
                            $newStorage->updated_at = date('Y-m-d H:i:s');
                        }
                        if (!$newStorage->save()) {
                            throw new \Exception('Ошибка обновления склада для нового материала');
                        }

                        // Записываем в историю операцию добавления нового количества
                        $storageHistory = new MaterialStorageHistory();
                        $storageHistory->material_storage_id = $newStorage->id;
                        $storageHistory->material_id = $model->material_id;
                        $storageHistory->quantity = $model->quantity;
                        $storageHistory->created_at = date('Y-m-d H:i:s');
                        $storageHistory->add_user_id = Yii::$app->user->id;
                        $storageHistory->type = MaterialStorageHistory::TYPE_INCOME;

                        if (!$storageHistory->save()) {
                            throw new \Exception('Ошибка сохранения истории склада при добавлении');
                        }
                    } else {
                        // Если material_id не изменился, обновляем количество
                        $storage = MaterialStorage::findByMaterialAndDate($model->material_id, $invoiceDate);
                        if ($storage) {
                            // Записываем в историю операцию удаления старого количества
                            if ($invoiceDetail->quantity > 0) {
                                $storageHistory = new MaterialStorageHistory();
                                $storageHistory->material_storage_id = $storage->id;
                                $storageHistory->material_id = $model->material_id;
                                $storageHistory->quantity = -$invoiceDetail->quantity;
                                $storageHistory->created_at = date('Y-m-d H:i:s');
                                $storageHistory->add_user_id = Yii::$app->user->id;
                                $storageHistory->type = MaterialStorageHistory::TYPE_OUTCOME;

                                if (!$storageHistory->save()) {
                                    throw new \Exception('Ошибка сохранения истории склада при удалении');
                                }
                            }

                            // Обновляем количество
                            $storage->quantity = $storage->quantity - $invoiceDetail->quantity + $model->quantity;
                            if ($storage->quantity < 0) {
                                throw new \Exception("Невозможно обновить накладную. Материал #{$model->material_id} уже был использован.");
                            }
                            $storage->updated_at = date('Y-m-d H:i:s');
                            if (!$storage->save()) {
                                throw new \Exception('Ошибка обновления склада');
                            }

                            // Записываем в историю операцию добавления нового количества
                            if ($model->quantity > 0) {
                                $storageHistory = new MaterialStorageHistory();
                                $storageHistory->material_storage_id = $storage->id;
                                $storageHistory->material_id = $model->material_id;
                                $storageHistory->quantity = $model->quantity;
                                $storageHistory->created_at = date('Y-m-d H:i:s');
                                $storageHistory->add_user_id = Yii::$app->user->id;
                                $storageHistory->type = MaterialStorageHistory::TYPE_INCOME;

                                if (!$storageHistory->save()) {
                                    throw new \Exception('Ошибка сохранения истории склада при добавлении');
                                }
                            }
                        }
                    }

                    // Обновляем детали накладной
                    $invoiceDetail->material_id = $model->material_id;
                    $invoiceDetail->quantity = $model->quantity;
                    $invoiceDetail->remainder_quantity = $model->quantity;
                    $invoiceDetail->price = $model->price;
                    if (!$invoiceDetail->save()) {
                        throw new \Exception('Ошибка обновления деталей накладной');
                    }

                    // Обновляем общую сумму в накладной
                    $totalAmount = 0;
                    foreach ($invoice->invoiceDetails as $detail) {
                        if ($detail->deleted_at === null) {
                            $totalAmount += (float)$detail->price * (int)$detail->quantity;
                        }
                    }
                    
                    $invoice->total_amount = $totalAmount;
                    $invoice->description = $model->description;
                    if (!$invoice->save()) {
                        throw new \Exception('Ошибка обновления накладной');
                    }

                    // Логируем действие
                    ActionLogger::actionLog(
                        'update_material',
                        'invoice_detail',
                        $invoiceDetail->id,
                        [
                            'invoice_id' => $invoice->id,
                            'invoice_number' => $invoice->invoice_number,
                            'material_id' => $model->material_id,
                            'quantity' => $model->quantity,
                            'price' => $model->price,
                            'old_material_id' => $invoiceDetail->getOldAttribute('material_id'),
                            'old_quantity' => $invoiceDetail->getOldAttribute('quantity'),
                            'old_price' => $invoiceDetail->getOldAttribute('price')
                        ]
                    );

                    $transaction->commit();
                    return [
                        'status' => 'success',
                        'message' => Yii::t('app', 'record_successfully_updated')
                    ];

                } catch (\Exception $e) {
                    $transaction->rollBack();
                    return [
                        'status' => 'error',
                        'message' => $e->getMessage()
                    ];
                }
            }
            return [
                'status' => 'error',
                'errors' => $model->getErrors()
            ];
        } 
    }

    

    public function actionSearch()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
        
        $date_from = Yii::$app->request->post('date_from');
        $date_to = Yii::$app->request->post('date_to');
        $type = Yii::$app->request->post('type');
        $supplier_id = Yii::$app->request->post('supplier_id');

        if ($type === 'payments') {
            $query = SupplierPayments::find()
                ->select([
                    'supplier_payments.*',
                    'supplier.full_name as supplier_name',
                    'users.full_name as user_name',
                    'expenses_type.name as expense_type_name'
                ])
                ->leftJoin('supplier', 'supplier_payments.supplier_id = supplier.id')
                ->leftJoin('users', 'supplier_payments.add_user_id = users.id')
                ->leftJoin('expenses', 'supplier_payments.expense_id = expenses.id')
                ->leftJoin('expenses_type', 'expenses.expense_type_id = expenses_type.id');

            if ($date_from && $date_to) {
                $query->andWhere(['>=', 'DATE(supplier_payments.created_at)', $date_from])
                      ->andWhere(['<=', 'DATE(supplier_payments.created_at)', $date_to]);
            }

            if ($supplier_id) {
                $query->andWhere(['supplier_payments.supplier_id' => $supplier_id]);
            }

            $payments = $query->orderBy(['supplier_payments.created_at' => SORT_DESC])
                            ->asArray()
                            ->all();

            return [
                'status' => 'success',
                'content' => $this->renderPartial('_payments_grid', [
                    'payments' => $payments
                ])
            ];
        } else {
            $query = Invoice::find()
                ->select([
                    'invoice.*',
                    'invoice_detail.quantity',
                    'invoice_detail.price',
                    'supplier.full_name as supplier_name',
                    'users.full_name as accepted_user',
                    'material.name as material_name',
                    'security_records.car_number',
                ])
                ->leftJoin('supplier', 'invoice.supplier_id = supplier.id')
                ->leftJoin('users', 'invoice.accept_user_id = users.id')
                ->leftJoin('invoice_detail', 'invoice.id = invoice_detail.invoice_id')
                ->leftJoin('material', 'invoice_detail.material_id = material.id')
                ->leftJoin('security_records', 'invoice.supplier_id = security_records.id');

            if ($date_from && $date_to) {
                $query->andWhere(['>=', 'DATE(invoice.created_at)', $date_from])
                      ->andWhere(['<=', 'DATE(invoice.created_at)', $date_to]);
            }

            if ($supplier_id) {
                $query->andWhere(['invoice.supplier_id' => $supplier_id]);
            }

            $materials = $query->orderBy(['invoice.created_at' => SORT_DESC])
                             ->asArray()
                             ->all();

            return [
                'status' => 'success',
                'content' => $this->renderPartial('_materials_grid', [
                    'materials' => $materials
                ])
            ];
        }
    }


    public function actionDelete()
    {
        Yii::$app->response->format = Response::FORMAT_JSON;
    
        if (Yii::$app->request->isPost) {
            $data = Yii::$app->request->post();
            $id = $data['SupplierPayments']['id'];
    
            $tracking = Tracking::find()
                ->where(['process_id' => $id])
                ->andWhere(['progress_type' => Tracking::PAY_FOR_SUPPLIER])
                ->andWhere(['is', 'accepted_at', null])
                ->andWhere(['is', 'deleted_at', null])
                ->one();
    
            if (!$tracking) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Cannot delete confirmed payment')
                ];
            }
    
            $model = SupplierPayments::findOne($id);
            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Record not found')
                ];
            }
    
            $transaction = Yii::$app->db->beginTransaction();
            try {
                $supplierBalance = SupplierBalance::findOne(['supplier_id' => $model->supplier_id]);
                if ($supplierBalance) {
                    $oldAmount = $supplierBalance->amount;
                    $supplierBalance->amount -= $model->amount;
    
                    if (!$supplierBalance->save()) {
                        throw new \Exception(Yii::t('app', 'Error updating supplier balance'));
                    }
    
                    $balanceHistory = new SupplierBalanceHistory();
                    $balanceHistory->supplier_id = $model->supplier_id;
                    $balanceHistory->amount = $supplierBalance->amount;
                    $balanceHistory->old_amount = $oldAmount;
                    $balanceHistory->type = $model->type;
    
                    if (!$balanceHistory->save()) {
                        throw new \Exception(Yii::t('app', 'Error saving balance history'));
                    }
                }
    
                // Используем валюту поставщика или валюту по умолчанию (ID = 2)
            $supplier = Supplier::findOne($model->supplier_id);
            $currencyId = $supplier && $supplier->currency_id ? $supplier->currency_id : 2;
            $cashbox = Cashbox::findByPaymentTypeAndCurrency($model->type, $currencyId);
                if ($cashbox) {
                    $cashbox->balance += $model->amount;
                    if (!$cashbox->save()) {
                        throw new \Exception(Yii::t('app', 'Error updating cashbox'));
                    }
                }

                $cashboxDetail = new CashboxDetail();
                $cashboxDetail->cashbox_id = $cashbox->id;
                $cashboxDetail->add_user_id = Yii::$app->user->id;
                $cashboxDetail->type = CashboxDetail::TYPE_IN; 
                $cashboxDetail->amount = $model->amount;
                $cashboxDetail->created_at = date('Y-m-d H:i:s');
                
                if (!$cashboxDetail->save()) {
                    throw new \Exception('Касса деталини сақлашда хатолик: ' . json_encode($cashboxDetail->getErrors(), JSON_UNESCAPED_UNICODE));
                }
    
                $tracking->deleted_at = date('Y-m-d H:i:s');
                if (!$tracking->save()) {
                    throw new \Exception(Yii::t('app', 'Error updating tracking'));
                }
    
                $model->deleted_at = date('Y-m-d H:i:s');
                if (!$model->save()) {
                    throw new \Exception(Yii::t('app', 'Error deleting payment'));
                }
    
                if ($model->expense_id) {
                    $expense = Expenses::findOne($model->expense_id);
                    if ($expense) {
                        $expense->deleted_at = date('Y-m-d H:i:s');
                        if (!$expense->save()) {
                            throw new \Exception(Yii::t('app', 'Error deleting expense'));
                        }
                    }
                }
    
                $transaction->commit();
                return [
                    'status' => 'success',
                    'message' => Yii::t('app', 'record_successfully_deleted')
                ];
            } catch (\Exception $e) {
                $transaction->rollBack();
                return [
                    'status' => 'error',
                    'message' => $e->getMessage()
                ];
            }
        } elseif (Yii::$app->request->isGet) {
            $id = Yii::$app->request->get('id');
            $model = SupplierPayments::findOne($id);
            if (!$model) {
                return [
                    'status' => 'error',
                    'message' => Yii::t('app', 'Record not found')
                ];
            }
            return [
                'status' => 'success',
                'content' => $this->renderPartial('delete', ['model' => $model]),
            ];
        }
    
        return [
            'status' => 'error',
            'message' => Yii::t('app', 'Error deleting record')
        ];
    }
}
