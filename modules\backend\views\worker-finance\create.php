<?php
use app\common\models\Cashbox;
use app\common\models\PaymentType;
use app\common\models\CurrencyCourse;
use app\modules\backend\models\WorkerFinances;
use yii\helpers\Html;
use app\assets\Select2Asset;
$type_payments = WorkerFinances::getTypes();
Select2Asset::register($this);
?>

<div class="worker-finance-form">
    <form id="worker-finance-create-form">

        <div class="form-group">
            <label for="worker_id"><?= Yii::t('app', 'worker') ?></label>
            <select id="worker_id" name="WorkerFinanceValidationForm[worker_id]" class="form-control select2" required>
                <option value=""><?= Yii::t('app', 'select_worker') ?></option>
                <?php foreach ($workers as $worker): ?>
                    <option value="<?= $worker['id'] ?>"><?= Html::encode($worker['full_name']) ?></option>
                <?php endforeach; ?>
            </select>
            <div class="error-container" id="worker_id-error"></div>
        </div>

        <div class="form-group">
            <label for="type_payment"><?= Yii::t('app', 'type_payment') ?></label>
            <select id="type_payment" name="WorkerFinanceValidationForm[payment_type]" class="form-control select2" required>
                <option value=""><?= Yii::t('app', 'select_type') ?></option>
                <?php foreach ($type_payments as $value => $label): ?>
                    <option value="<?= $value ?>"><?= $label ?></option>
                <?php endforeach; ?>
            </select>
            <div class="error-container" id="payment_type-error"></div>
        </div>

        <div class="form-group">
            <label for="month"><?= Yii::t('app', 'month') ?></label>
            <input type="month" id="month" name="WorkerFinanceValidationForm[month]" class="form-control" required readonly>
            <div class="error-container" id="month-error"></div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="payment_type"><?= Yii::t('app', 'Payment type') ?></label>
                    <select id="payment_type" name="WorkerFinanceValidationForm[type]" class="form-control select2" required>
                        <option value=""><?= Yii::t('app', 'select_type') ?></option>
                        <?php foreach (PaymentType::getTypeLabels() as $value => $label): ?>
                            <option value="<?= $value ?>" <?= $value == 1 ? 'selected' : '' ?>><?= $label ?></option>
                        <?php endforeach; ?>
                    </select>
                    <div class="error-container" id="type-error"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="cashbox_id"><?= Yii::t('app', 'cashbox') ?></label>
                    <select id="cashbox_id" name="WorkerFinanceValidationForm[cashbox_id]" class="form-control select2" required>
                        <option value=""><?= Yii::t('app', 'select') ?></option>
                        <?php 
                        // Выплаты сотрудникам производятся только в сомах (ID=2)
                        $cashboxes = Cashbox::find()
                            ->where(['deleted_at' => null])
                            ->andWhere(['currency_id' => 2])
                            ->all();
                        
                        foreach ($cashboxes as $cashbox): ?>
                            <option value="<?= $cashbox->id ?>" 
                                    data-currency-id="<?= $cashbox->currency_id ?>"
                                    data-currency-name="<?= $cashbox->currency ? $cashbox->currency->name : 'N/A' ?>"
                                    data-balance="<?= $cashbox->balance ?>">
                                <?= $cashbox->title ?> (<?= $cashbox->currency ? $cashbox->currency->name : 'Без валюты' ?>) - Баланс: <?= number_format($cashbox->balance, 2) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="error-container" id="cashbox_id-error"></div>
                </div>
            </div>
        </div>

        <!-- Информация о конвертации валют -->
        <div id="worker-conversion-info" class="row" style="display: none;">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <h6><i class="fas fa-exchange-alt"></i> <?= Yii::t('app', 'currency_conversion') ?></h6>
                    <div id="worker-conversion-details"></div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="amount"><?= Yii::t('app', 'amount') ?></label>
            <input type="text" id="amount" name="WorkerFinanceValidationForm[amount]" class="form-control formatted-numeric-input" required>
            <small class="form-text text-muted" id="amount_hint"></small>
            <div class="error-container" id="amount-error"></div>
        </div>

        <div class="form-group deduct-from-salary-group" style="display: none;">
            <div class="form-check">
                <input type="hidden" name="WorkerFinanceValidationForm[deduct_from_salary]" value="0">
                <input type="checkbox" class="form-check-input" id="deduct_from_salary" name="WorkerFinanceValidationForm[deduct_from_salary]" value="1">
                <label class="form-check-label" for="deduct_from_salary"><?= Yii::t('app', 'Deduct from salary') ?></label>
            </div>
            <div class="deduction-details mt-2" style="display: none;">
                
                <div class="form-group">
                    <label for="deduct_amount"><?= Yii::t('app', 'Deduction amount') ?></label>
                    <input type="text" id="deduct_amount" name="WorkerFinanceValidationForm[deduct_amount]" class="form-control" placeholder="Введите сумму удержания">
                    <small class="form-text text-muted" id="deduct_amount_hint"></small>
                    <div class="error-container" id="deduct_amount-error"></div>
                </div>

                <div>
                    <div id="debt_hint"></div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="description"><?= Yii::t('app', 'description') ?></label>
            <textarea id="description" name="WorkerFinanceValidationForm[description]" class="form-control"></textarea>
            <div class="error-container" id="description-error"></div>
        </div>
    </form>
    <div id="form-error-message"  style="display: none;"></div>
</div>


<?php
$remaind_salary_this_month = Yii::t('app', 'remaind_salary_this_month');
$amount_debt = Yii::t('app', 'amount_debt');
$remaind_amount = Yii::t('app', 'remaind_amount');
?>


<script>
    var remaind_salary_this_month = '<?= $remaind_salary_this_month ?>';
    var amount_debt = '<?= $amount_debt ?>';
    var remaind_amount = '<?= $remaind_amount ?>';


   

if (typeof TYPE_SALARY === 'undefined') {
    var TYPE_SALARY = 1;
    var TYPE_ADVANCE = 2;
    var TYPE_BONUS = 3;
    var TYPE_DEBT = 4;
    var TYPE_ONE_TIME_PAYMENT = 5;
}

$(document).ready(function() {

    // Функция для удаления минуса из значения
    function removeNegative(input) {
        var value = input.value;
        if (value.startsWith('-')) {
            input.value = value.substring(1);
        }
    }

    // Обработчики для поля amount
    $('#amount').on('input', function() {
        removeNegative(this);
    });

    $('#amount').on('paste', function(e) {
        var pastedText = (e.originalEvent || e).clipboardData.getData('text/plain');
        if (pastedText.startsWith('-')) {
            e.preventDefault();
            $(this).val(pastedText.substring(1));
        }
    });

    // Специальная функция для форматирования поля удержания
    function formatDeductionAmount(input) {
        var rawValue = $(input).val().replace(/[\s\u00A0]/g, '').replace(',', '.');
        rawValue = rawValue.replace(/[^0-9.]/g, ''); // Оставляем только цифры и точку
        
        // Убираем лишние точки
        var parts = rawValue.split('.');
        if (parts.length > 2) {
            rawValue = parts[0] + '.' + parts.slice(1).join('');
        }
        
        // Удаляем ведущие нули
        if (rawValue.length > 1 && rawValue.startsWith('0') && !rawValue.startsWith('0.')) {
            rawValue = rawValue.replace(/^0+/, '');
            if (rawValue.startsWith('.')) {
                rawValue = '0' + rawValue;
            }
            if (!rawValue) rawValue = '0';
        }
        
        // Если начинается с точки, добавляем 0
        if (rawValue.startsWith('.')) {
            rawValue = '0' + rawValue;
        }
        
        // Сохраняем чистое значение
        input.dataset.rawValue = rawValue;
        
        // Форматируем для отображения
        if (rawValue && !isNaN(parseFloat(rawValue))) {
            var numberParts = rawValue.split('.');
            var integerPart = numberParts[0];
            var decimalPart = numberParts[1];
            
            var formattedInteger = parseInt(integerPart, 10).toLocaleString('ru-RU');
            $(input).val(formattedInteger + (decimalPart !== undefined ? '.' + decimalPart : ''));
        } else {
            $(input).val('');
        }
    }

    // СТРОГИЙ КОНТРОЛЬ ПОЛЯ УДЕРЖАНИЯ
    $('#deduct_amount').off().on('input', function() {
        var maxDeduction = parseFloat($(this).attr('max')) || 0;
        var currentValue = $(this).val().replace(/[\s\u00A0]/g, '').replace(',', '.');
        var numericValue = parseFloat(currentValue) || 0;
        
        // Если превышает максимум - обрезаем до максимума
        if (maxDeduction > 0 && numericValue > maxDeduction) {
            $(this).val(maxDeduction);
        }
    });
    
    $('#deduct_amount').on('keydown', function(e) {
        var maxDeduction = parseFloat($(this).attr('max')) || 0;
        
        // Разрешенные клавиши
        var allowedKeys = ['Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight', 'Home', 'End'];
        if (allowedKeys.includes(e.key) || (e.ctrlKey && ['a', 'c', 'v', 'x'].includes(e.key))) {
            return true;
        }
        
        // Только цифры и точка
        if (!/[0-9.]/.test(e.key)) {
            e.preventDefault();
            return false;
        }
        
        // Проверяем превышение при добавлении цифры
        if (maxDeduction > 0) {
            var currentVal = $(this).val();
            var newVal = currentVal + e.key;
            var newNumericVal = parseFloat(newVal.replace(/[\s\u00A0]/g, '').replace(',', '.')) || 0;
            
            if (newNumericVal > maxDeduction) {
                e.preventDefault();
                return false;
            }
        }
    });
    
    $('#deduct_amount').on('paste', function(e) {
        var self = this;
        setTimeout(function() {
            var maxDeduction = parseFloat($(self).attr('max')) || 0;
            var value = parseFloat($(self).val().replace(/[\s\u00A0]/g, '').replace(',', '.')) || 0;
            if (maxDeduction > 0 && value > maxDeduction) {
                $(self).val(maxDeduction);
            }
        }, 10);
    });

    $('.select2').select2();
    
    // Сохраняем оригинальные опции
    var originalOptions = {};
    $('.select2').each(function() {
        var id = $(this).attr('id');
        originalOptions[id] = $(this).find('option').clone();
    });
    // Функция для получения месяца и суммы 
    function getMonthAndAmount(workerId, type) {
        $.ajax({
            url: '/backend/worker-finance/get-last-payment-month',
            type: 'GET',
            data: { 
                worker_id: workerId,
                type: type
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    $('#month').val(response.month);
                    if (response.remaining > 0) {
                        $('#amount_hint').text(remaind_salary_this_month + ': ' + response.remaining.toFixed(2) + ' сўм');
                    } else {
                        $('#amount_hint').text('');
                    }
                }
            }
        });
    }

    $('#worker_id').on('change', function() {
        var workerId = $(this).val();
        var $select = $('#type_payment');
        
        if (workerId) {
            $.ajax({
                url: '/backend/worker-finance/check-salary',
                type: 'GET',
                data: { worker_id: workerId },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        if (response.has_salary) {
                            $select.empty().append(originalOptions['type_payment'].clone());
                            $select.val('1').trigger('change');
                            $('#month').prop('readonly', true);
                            getMonthAndAmount(workerId, 1);
                        } else {
                            var emptyOption = originalOptions['type_payment'].filter('[value=""]').clone();
                            var bonusOption = originalOptions['type_payment'].filter('[value="3"]').clone();
                            var oneTimePaymentOption = originalOptions['type_payment'].filter('[value="5"]').clone();
                            $select.empty()
                                  .append(emptyOption)
                                  .append(bonusOption)
                                  .append(oneTimePaymentOption);
                            $('#month').prop('readonly', false);
                            $('#month').val('');
                            $('#amount_hint').text('');
                            $('#amount').val('');
                        }
                    }
                }
            });
        } else {
            $select.empty()
                  .append(originalOptions['type_payment'].clone())
                  .val('')
                  .trigger('change');
            $('#month').val('');
            $('#month').prop('readonly', true);
            $('#amount').attr('placeholder', 'Қолган сумма: 0');
            $('#amount').val('');
        }
    });

    // При изменении типа оплаты
    $('#type_payment').on('change', function() {
        var workerId = $('#worker_id').val();
        var type = $(this).val();

        if (workerId && type) {
            if (type == TYPE_SALARY || type == TYPE_ADVANCE) {
                // Для зарплаты и аванса
                $('.form-group:has(#month)').show();
                $('.deduct-from-salary-group').show(); // Показываем чекбокс для вычета долга
                $('#month').prop('readonly', true);
                getMonthAndAmount(workerId, type);
            } else if (type == TYPE_BONUS) {
                // Для бонуса
                $('.form-group:has(#month)').show();
                $('.deduct-from-salary-group').hide(); // Скрываем чекбокс
                $('#month').prop('readonly', false);
                $('#month').val('');
                $('#amount_hint').text('');
                $('#amount').val('');
            } else if (type == TYPE_DEBT) {
                // Для долга
                $('.form-group:has(#month)').hide();
                $('.deduct-from-salary-group').hide(); // Скрываем чекбокс
                $('#month').val('');
                $('#amount_hint').text('');
                $('#amount').val('');
            } else if (type == TYPE_ONE_TIME_PAYMENT) {
                // Для разового платежа
                $('.form-group:has(#month)').show();
                $('.deduct-from-salary-group').hide(); // Скрываем чекбокс
                $('#month').prop('readonly', true);
                var currentDate = new Date();
                var currentMonth = currentDate.getFullYear() + '-' + String(currentDate.getMonth() + 1).padStart(2, '0');
                $('#month').val(currentMonth);
                $('#amount_hint').text('');
                $('#amount').val('');
            }
        }
    });

    // При изменении чекбокса вычета из зарплаты
    $('#deduct_from_salary').on('change', function() {
        if ($(this).is(':checked')) {
            // Получаем активные долги работника
            var workerId = $('#worker_id').val();
            $.ajax({
                url: '/backend/worker-finance/get-active-debts',
                type: 'GET',
                data: { worker_id: workerId },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success' && response.debts.length > 0) {
                        $('.deduction-details').show();
                        $('#deduct_amount').prop('required', true);
                        
                        // Инициализируем форматирование для появившегося поля
                        setTimeout(function() {
                            if (typeof initializeModalNumberFormatting === 'function') {
                                initializeModalNumberFormatting();
                            }
                        }, 100);
                        
                        // Показываем сумму всех долгов
                        var totalDebt = response.debts.reduce(function(sum, debt) {
                            return sum + parseFloat(debt.amount);
                        }, 0);
                        
                        // Показываем сумму зарплаты/аванса и долгов  
                        var salaryRawValue = $('#amount')[0].dataset.rawValue || $('#amount').val().replace(/[\s\u00A0]/g, '').replace(',', '.');
                        var salaryAmount = parseFloat(salaryRawValue) || 0;
                        $('#debt_hint').html(amount_debt + ': ' + totalDebt.toFixed(2) + ' сўм');
                        
                        // Устанавливаем максимальное значение для поля вычета
                        var maxDeduction = Math.min(totalDebt, salaryAmount);
                        $('#deduct_amount').attr('max', maxDeduction);
                        $('#deduct_amount_hint').text('Максимальная сумма удержания: ' + maxDeduction.toFixed(2) + ' сўм');
                    } else {
                        $(this).prop('checked', false);
                        alert('У работника нет активных долгов');
                    }
                }
            });
        } else {
            $('.deduction-details').hide();
            $('#deduct_amount').prop('required', false);
            $('#deduct_amount').val('');
            $('#deduct_amount_hint').text('');
            getMonthAndAmount($('#worker_id').val(), $('#type_payment').val());
        }
    });

    // Функция для обновления информации о кассе и балансе
    function updateWorkerFinanceInfo() {
        var cashboxSelect = $('#cashbox_id');
        var amountInput = $('#amount');
        
        var cashboxBalance = cashboxSelect.find(':selected').data('balance');
        var cashboxCurrencyName = cashboxSelect.find(':selected').data('currency-name');
        
        var amountValue = amountInput.val() || '';
        var cleanAmount = amountValue.replace(/[\s\u00A0]/g, '').replace(',', '.');
        var amount = parseFloat(cleanAmount) || 0;
        
        if (!cashboxBalance || amount <= 0) {
            $('#worker-conversion-info').hide();
            return;
        }
        
        var details = `
            <p><strong>Выплата сотруднику:</strong> ${formatNumber(amount)} ${cashboxCurrencyName}</p>
            <p><strong>Будет списано с кассы:</strong> ${formatNumber(amount)} ${cashboxCurrencyName}</p>
            <p><strong>Баланс кассы:</strong> ${formatNumber(cashboxBalance)} ${cashboxCurrencyName}</p>
            ${cashboxBalance < amount ? '<p class="text-danger"><strong>Недостаточно средств в кассе!</strong></p>' : ''}
        `;
        
        $('#worker-conversion-details').html(details);
        $('#worker-conversion-info').show();
    }
    
    function formatNumber(num) {
        return Number(num).toLocaleString('ru-RU', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }
    
    // Обновляем информацию при изменении полей
    $('#cashbox_id, #amount').on('change keyup', updateWorkerFinanceInfo);

    // Обновляем максимальную сумму удержания при изменении зарплаты
    $('#amount').on('input change', function() {
        if ($('#deduct_from_salary').is(':checked')) {
            var salaryValue = $(this).val().replace(/[\s\u00A0]/g, '').replace(',', '.');
            var salaryAmount = parseFloat(salaryValue) || 0;
            
            var debtText = $('#debt_hint').text();
            var totalDebt = debtText ? parseFloat(debtText.match(/\d+\.?\d*/)[0]) : 0;
            var maxDeduction = Math.min(totalDebt, salaryAmount);
            
            $('#deduct_amount').attr('max', maxDeduction);
            $('#deduct_amount_hint').text('Максимальная сумма удержания: ' + maxDeduction.toFixed(2) + ' сўм');
            
            // Проверяем текущее значение удержания
            var currentDeduction = parseFloat($('#deduct_amount').val()) || 0;
            if (currentDeduction > maxDeduction) {
                $('#deduct_amount').val(maxDeduction);
            }
        }
    });
});
</script>