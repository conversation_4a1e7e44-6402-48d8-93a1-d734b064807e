<?php
use app\assets\Select2Asset;
use app\common\models\PaymentType;
use app\common\models\Cashbox;
use app\common\models\CurrencyCourse;
use yii\bootstrap5\Html;

Select2Asset::register($this);
?>

<div class="supplier-payment-form">
    <form id="supplier-pay-form">
        <input type="hidden" name="SupplierPayForm[supplier_id]" value="<?= $model->supplier_id ?>">

        <?php if ($supplier->currency_id == 1): ?>
            <div class="form-group">
                <label for="currency"><?= Yii::t('app', 'currency') ?></label>
                <input type="text" id="currency" name="SupplierPayForm[currency]" class="form-control" value="<?= Html::encode($currencyName) ?>" disabled>
            </div>

            <div class="form-group">
                <label for="course"><?= Yii::t('app', 'course') ?></label>
                <input type="text" id="course" name="SupplierPayForm[course]" class="form-control formatted-numeric-input" value="<?= $courseModel->course ?>">
                <div class="error-container" id="course-error"></div>
            </div>
        <?php endif; ?>

        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label for="cashbox_id"><?= Yii::t('app', 'cashbox') ?></label>
                    <select id="cashbox_id" name="SupplierPayForm[cashbox_id]" class="form-control select2" required>
                        <option value=""><?= Yii::t('app', 'select') ?></option>
                        <?php 
                        // Фильтруем кассы по валюте поставщика
                        $requiredCurrencyId = $supplier->currency_id == 1 ? 1 : 2; // Если поставщик в долларах - показываем только доллары, иначе только сомы
                        
                        $cashboxes = Cashbox::find()
                            ->where(['deleted_at' => null])
                            ->andWhere(['currency_id' => $requiredCurrencyId])
                            ->all();
                        
                        foreach ($cashboxes as $cashbox): ?>
                            <option value="<?= $cashbox->id ?>" 
                                    data-currency-id="<?= $cashbox->currency_id ?>"
                                    data-currency-name="<?= $cashbox->currency ? $cashbox->currency->name : 'N/A' ?>"
                                    data-balance="<?= $cashbox->balance ?>">
                                <?= $cashbox->title ?> (<?= $cashbox->currency ? $cashbox->currency->name : 'Без валюты' ?>) - Баланс: <?= number_format($cashbox->balance, 2) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="error-container" id="cashbox_id-error"></div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label for="type"><?= Yii::t('app', 'payment type') ?></label>
                    <select id="type" name="SupplierPayForm[type]" class="form-control select2" required>
                        <option value=""><?= Yii::t('app', 'select_type') ?></option>
                        <?php foreach (PaymentType::getTypeLabels() as $value => $label): ?>
                            <option value="<?= $value ?>"><?= $label ?></option>
                        <?php endforeach; ?>
                    </select>
                    <div class="error-container" id="type-error"></div>
                </div>
            </div>
        </div>

        <!-- Информация о конвертации валют -->
        <div id="supplier-conversion-info" class="row" style="display: none;">
            <div class="col-md-12">
                <div class="alert alert-info">
                    <h6><i class="fas fa-exchange-alt"></i> <?= Yii::t('app', 'currency_conversion') ?></h6>
                    <div id="supplier-conversion-details"></div>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label for="summa"><?= Yii::t('app', 'amount') ?></label>
            <input type="text" id="summa" name="SupplierPayForm[amount]" class="form-control formatted-numeric-input" step="0.01" min="0" required>
            <div class="error-container" id="amount-error"></div>
        </div>

    </form>
    <div id="form-error-message" style="display: none;"></div>
</div>

<script>
// Получаем курсы валют для JavaScript
<?php
$courses = [];
$allCourses = CurrencyCourse::find()
    ->where(['<=', 'start_date', date('Y-m-d')])
    ->andWhere([
        'or',
        ['>', 'end_date', date('Y-m-d')],
        ['end_date' => null]
    ])
    ->andWhere(['deleted_at' => null])
    ->all();

foreach ($allCourses as $course) {
    $courses[$course->currency_id] = $course->course;
}

// Убеждаемся что курс базовой валюты (доллар) присутствует
if (!isset($courses[1])) {
    $courses[1] = 1; // Доллар - базовая валюта
}
?>
window.supplierCurrencyCourses = <?= json_encode($courses) ?>;
window.supplierCurrencyId = <?= $supplier->currency_id ?>;

$(document).ready(function() {
    $('.select2').select2({
        width: '100%',
        placeholder: '<?= Yii::t("app", "select") ?>',
        allowClear: true,
        dropdownParent: $('#ideal-mini-modal'),
        language: {
            noResults: function() {
                return "<?= Yii::t('app', 'Nothing found') ?>";
            },
            searching: function() {
                return "<?= Yii::t('app', 'Searching...') ?>";
            }
        },
        minimumInputLength: 0,
        minimumResultsForSearch: 1
    });

    // Инициализация форматирования числовых полей
    $('.formatted-numeric-input').on('input', function() {
        var value = $(this).val().replace(/\s+/g, '');
        var formattedValue = value.replace(/\B(?=(\d{3})+(?!\d))/g, ' ');
        $(this).val(formattedValue);
    });

    function updateSupplierConversionInfo() {
        var cashboxSelect = $('#cashbox_id');
        var amountInput = $('#summa');
        
        var cashboxCurrencyId = cashboxSelect.find(':selected').data('currency-id');
        var cashboxCurrencyName = cashboxSelect.find(':selected').data('currency-name');
        var cashboxBalance = cashboxSelect.find(':selected').data('balance');
        
        var amountValue = amountInput.val() || '';
        var cleanAmount = amountValue.replace(/[\s\u00A0]/g, '').replace(',', '.');
        var amount = parseFloat(cleanAmount) || 0;
        
        if (!cashboxCurrencyId || amount <= 0) {
            $('#supplier-conversion-info').hide();
            return;
        }
        
        // Для поставщиков в долларах - оплата в долларах, для остальных - в сомах
        if (window.supplierCurrencyId == 1) {
            // Поставщик в долларах - касса тоже в долларах
            if (cashboxCurrencyId == 1) {
                var details = `
                    <p><strong>Оплата поставщику:</strong> ${formatNumber(amount)} ${cashboxCurrencyName}</p>
                    <p><strong>Будет списано с кассы:</strong> ${formatNumber(amount)} ${cashboxCurrencyName}</p>
                    <p><strong>Баланс кассы:</strong> ${formatNumber(cashboxBalance)} ${cashboxCurrencyName}</p>
                    ${cashboxBalance < amount ? '<p class="text-danger"><strong>Недостаточно средств в кассе!</strong></p>' : ''}
                `;
                
                $('#supplier-conversion-details').html(details);
                $('#supplier-conversion-info').show();
            }
        } else {
            // Поставщик не в долларах - касса в сомах
            if (cashboxCurrencyId == 2) {
                var details = `
                    <p><strong>Оплата поставщику:</strong> ${formatNumber(amount)} ${cashboxCurrencyName}</p>
                    <p><strong>Будет списано с кассы:</strong> ${formatNumber(amount)} ${cashboxCurrencyName}</p>
                    <p><strong>Баланс кассы:</strong> ${formatNumber(cashboxBalance)} ${cashboxCurrencyName}</p>
                    ${cashboxBalance < amount ? '<p class="text-danger"><strong>Недостаточно средств в кассе!</strong></p>' : ''}
                `;
                
                $('#supplier-conversion-details').html(details);
                $('#supplier-conversion-info').show();
            }
        }
    }
    
    function formatNumber(num) {
        return Number(num).toLocaleString('ru-RU', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }
    
    // Обновляем информацию при изменении полей
    $('#cashbox_id, #summa').on('change keyup', updateSupplierConversionInfo);
});
</script>