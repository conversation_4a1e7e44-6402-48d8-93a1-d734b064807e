<?php

use app\common\models\EquipmentPart;
use app\common\models\EquipmentPartPhoto;
use yii\helpers\Html;
use yii\widgets\Pjax;
use yii\web\View;
use app\assets\DataTablesAsset;
use yii\helpers\Url;

DataTablesAsset::register($this);

$this->title = Yii::t("app", "equipment_parts");
$this->params['breadcrumbs'][] = $this->title;

$searchLabel = Yii::t("app", "search:");
$lengthMenuLabel = Yii::t("app", "Show _MENU_ entries");
$zeroRecordsLabel = Yii::t("app", "Nothing found");
$infoLabel = Yii::t("app", "Showing _PAGE_ to _PAGES_ of _MAX_ items");
$infoEmptyLabel = Yii::t("app", "Nothing found");
$infoFilteredLabel = Yii::t("app", "(filtered from _MAX_ records)");
$all = Yii::t("app", "all");
$active = Yii::t("app", "active");
$inactive = Yii::t("app", "inactive");
$reserve = Yii::t("app", "reserve");
$purchased = Yii::t("app", "purchased");
$fromReserve = Yii::t("app", "reserve");

$statuses = EquipmentPart::getStatusLabels();
$sourceTypes = EquipmentPart::getSourceTypeLabels();
?>

<style>
    #part_filter.select2, #source_type_filter.select2, #equipment_filter.select2 {
        min-width: 145px !important;
        width: 100% !important;
    }

    /* Стили для контейнера Select2 (если используется библиотека Select2) */
    #part_filter + .select2-container, #source_type_filter + .select2-container, #equipment_filter + .select2-container {
        width: 145px !important;
    }

    /* Стили для полей ввода даты */
    input[type="date"].form-control {
        width: 150px;
    }

    /* Отступы между элементами */
    .d-flex.gap-2 {
        gap: 0.5rem !important;
    }

    /* Стили для карусели фотографий */
    .photo-carousel {
        border-radius: 4px;
        overflow: hidden;
        transition: box-shadow 0.3s ease;
    }
    
    .photo-carousel:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }
    
    .carousel-photo {
        border-radius: 4px;
        transition: opacity 0.3s ease;
    }
    
    .carousel-btn {
        opacity: 0;
        transition: opacity 0.3s ease, background-color 0.3s ease;
    }
    
    .photo-carousel:hover .carousel-btn {
        opacity: 1;
    }
    
    .carousel-btn:hover {
        background: rgba(0,0,0,0.9) !important;
        transform: translateY(-50%) scale(1.1);
    }
    
    .photo-counter {
        font-family: monospace;
        font-weight: bold;
    }
    
    /* Анимация переключения фотографий */
    .carousel-photo {
        transition: all 0.3s ease;
    }
</style>

<div class="card-body">
    <div class="row align-items-center mb-3">
        <div class="col-md-6">
            <h4 class="my-0"><?= Html::encode($this->title) ?></h4>
        </div>
        <div class="col-md-6 text-right">
            <div class="d-flex justify-content-end align-items-center gap-2">
                <?php if (Yii::$app->user->can('admin') || Yii::$app->user->can('technical_staff')): ?>
                    <a href="#" class="btn btn-primary part-create" data-toggle="modal" data-target="#ideal-mini-modal">
                        <?= Yii::t("app", "add_part") ?>
                    </a>
                    <a href="<?= Url::to(['/backend/equipment/index']) ?>" class="btn btn-success">
                        <?= Yii::t("app", "equipment") ?>
                    </a>
                <?php endif ?>
            </div>
        </div>
    </div>



    <?php Pjax::begin(['id' => 'part-grid-pjax']); ?>
    <?php if($result): ?>
        <div>
            <table id="part-grid-view" class="table table-bordered table-striped compact">
                <thead>
                    <th><?= Yii::t("app", "Photo") ?></th>
                    <th><?= Yii::t("app", "Name") ?></th>
                    <th><?= Yii::t("app", "Quantity") ?></th>
                    <th><?= Yii::t("app", "Price") ?></th>
                    <th><?= Yii::t("app", "last_purchase_date") ?></th>
                    <th><?= Yii::t("app", "Status") ?></th>
                    <th><?= Yii::t("app", "comment") ?></th>
                    <th><?= Yii::t("app", "actions") ?></th>
                </thead>
                <tbody>
                <?php foreach ($result as $model): ?>
                    <tr>                        <td>
                            <?php 
                            // Получаем все фотографии запчасти
                            $allPhotos = EquipmentPartPhoto::find()
                                ->where(['equipment_part_id' => $model['id'], 'deleted_at' => null])
                                ->orderBy(['is_main' => SORT_DESC, 'sort_order' => SORT_ASC])
                                ->all();
                              // Если нет фотографий в новой системе, используем старое поле
                            if (empty($allPhotos) && $model['photo']) {
                                $fallbackPhoto = new stdClass();
                                $fallbackPhoto->photoUrl = '/uploads/equipment_parts/' . Html::encode($model['photo']);
                                $allPhotos = [$fallbackPhoto];
                            }
                            
                            if (!empty($allPhotos)): ?>
                                <div class="photo-carousel" data-part-id="<?= $model['id'] ?>" style="position: relative; width: 50px; height: 50px;">
                                    <?php foreach ($allPhotos as $index => $photo): ?>
                                        <img src="<?= is_object($photo) ? $photo->photoUrl : $photo ?>"
                                             alt="Part Photo <?= $index + 1 ?>"
                                             class="carousel-photo part-photo"
                                             data-id="<?= $model['id'] ?>"
                                             data-index="<?= $index ?>"
                                             style="max-width: 50px; max-height: 50px; cursor: pointer; position: absolute; top: 0; left: 0; object-fit: cover; <?= $index > 0 ? 'display: none;' : '' ?>">
                                    <?php endforeach; ?>
                                    
                                    <?php if (count($allPhotos) > 1): ?>
                                        <!-- Кнопки навигации -->
                                        <button type="button" class="carousel-btn carousel-prev" 
                                                data-part-id="<?= $model['id'] ?>"
                                                style="position: absolute; left: -5px; top: 50%; transform: translateY(-50%); 
                                                       background: rgba(0,0,0,0.7); color: white; border: none; 
                                                       border-radius: 50%; width: 16px; height: 16px; font-size: 8px; 
                                                       cursor: pointer; z-index: 10;">‹</button>
                                        
                                        <button type="button" class="carousel-btn carousel-next" 
                                                data-part-id="<?= $model['id'] ?>"
                                                style="position: absolute; right: -5px; top: 50%; transform: translateY(-50%); 
                                                       background: rgba(0,0,0,0.7); color: white; border: none; 
                                                       border-radius: 50%; width: 16px; height: 16px; font-size: 8px; 
                                                       cursor: pointer; z-index: 10;">›</button>
                                        
                                        <!-- Индикатор количества фотографий -->
                                        <span class="photo-counter" 
                                              style="position: absolute; bottom: -2px; right: 0; 
                                                     background: rgba(0,0,0,0.7); color: white; 
                                                     font-size: 8px; padding: 1px 3px; border-radius: 2px;">
                                            <span class="current-photo">1</span>/<span class="total-photos"><?= count($allPhotos) ?></span>
                                        </span>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <span class="text-muted">No photo</span>
                            <?php endif; ?>
                        </td>
                <td><?= Html::encode($model['name']) ?></td>
                        <td>
                            <?php
                            $totalQuantity = $model['quantity'] ?? 0;
                            $assignedQuantity = $model['assigned_quantity'] ?? 0;
                            $availableQuantity = $totalQuantity - $assignedQuantity;
                            ?>
                            <span class="badge badge-<?= $totalQuantity > 0 ? 'success' : 'danger' ?>">
                                <?= Yii::t('app', 'Total') ?>: <?= $totalQuantity ?>
                            </span>
                            <?php if (isset($model['assigned_quantity'])): ?>
                                <br><span class="badge badge-warning">
                                    <?= Yii::t('app', 'Assigned') ?>: <?= $assignedQuantity ?>
                                </span>
                                <br><span class="badge badge-<?= $availableQuantity > 0 ? 'info' : 'secondary' ?>">
                                    <?= Yii::t('app', 'Available') ?>: <?= $availableQuantity ?>
                                </span>
                            <?php endif; ?>
                        </td>
                        <td><?= $model['price'] ? : 0  ?></td>
                        <td data-order="<?= $model['last_purchase_date'] ? strtotime($model['last_purchase_date']) : 0 ?>">
                            <?= $model['last_purchase_date'] ? date('d.m.Y', strtotime($model['last_purchase_date'])) : '-' ?>
                        </td>
                        <td>
                            <?php if (isset($model['status'])): ?>
                                <span class="badge badge-<?= $model['status'] == EquipmentPart::STATUS_INACTIVE ? 'danger' : ($model['status'] == EquipmentPart::STATUS_ACTIVE ? 'success' : 'warning') ?>">
                                    <?= $statuses[$model['status']] ?? Yii::t('app', 'unknown') ?>
                                </span>
                            <?php else: ?>
                                <span class="badge badge-info"><?= Yii::t('app', 'mixed') ?></span>
                            <?php endif; ?>
                        </td>
                        <td><?= Html::encode($model['comment'] ?? '-') ?></td>
                        <td>
                            <div class="dropdown d-inline">
                                <a href="#" class="badge badge-info dropdown-toggle" data-toggle="dropdown">
                                    <?php echo Yii::t("app", "detail"); ?>
                                </a>
                                <div class="dropdown-menu">                                    <?php if (!isset($model['deleted_at']) || $model['deleted_at'] == NULL): ?>
                                        <a href="#" class="dropdown-item part-update" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "edit") ?>
                                        </a>
                                        <a href="#" class="dropdown-item part-income" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "income") ?>
                                        </a>
                                        <a href="<?= \yii\helpers\Url::to(['/backend/equipment-part/history', 'id' => $model['id']]) ?>" class="dropdown-item">
                                            <?= Yii::t("app", "history") ?>
                                        </a>
                                        <a href="#" class="dropdown-item part-defect" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "defect") ?>
                                        </a>
                                        <a href="#" class="dropdown-item part-write-off" data-toggle="modal" data-target="#ideal-mini-modal" data-id="<?= Html::encode($model['id']) ?>">
                                            <?= Yii::t("app", "write_off") ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <p><?= Yii::t('app', 'no_data_available') ?></p>
    <?php endif; ?>
    <?php Pjax::end(); ?>
</div>

<div id="one" data-text="<?= Yii::t("app", "part_information") ?>"></div>
<div id="two" data-text="<?= Yii::t("app", "change_status") ?>"></div>
<div id="three" data-text="<?= Yii::t("app", "history") ?>"></div>
<div id="four" data-text="<?= Yii::t("app", "attach_to_equipment") ?>"></div>
<div id="five" data-text="<?= Yii::t("app", "detach_from_equipment") ?>"></div>
<div id="six" data-text="<?= Yii::t("app", "income") ?>"></div>
<div id="seven" data-text="<?= Yii::t("app", "defect") ?>"></div>
<?php


// Register the new JS file
use app\assets\Select2Asset;

$this->registerJsFile('@web/js/equipment-part-index.js', [
    'depends' => [\yii\web\JqueryAsset::class, DataTablesAsset::class, Select2Asset::class],
    'position' => View::POS_END
]);

// Pass translated strings and URLs to the script
$this->registerJs(
    'var datatablesI18n = ' . \yii\helpers\Json::htmlEncode([
        'search' => $searchLabel,
        'lengthMenu' => $lengthMenuLabel,
        'zeroRecords' => $zeroRecordsLabel,
        'info' => $infoLabel,
        'infoEmpty' => $infoEmptyLabel,
        'infoFiltered' => $infoFilteredLabel,
    ]) . ';',
    View::POS_HEAD,
    'datatables-i18n-options'
);

$this->registerJs(
    'var equipmentPartUrls = ' . \yii\helpers\Json::htmlEncode([
        'create' => Url::to(['/backend/equipment-part/create']),
        'update' => Url::to(['/backend/equipment-part/update']),
        'delete' => Url::to(['/backend/equipment-part/delete']),
        'search' => Url::to(['/backend/equipment-part/search']),
        'getPhoto' => Url::to(['/backend/equipment-part/get-photo']),
        'changeStatus' => Url::to(['/backend/equipment-part/change-status']),
        'attach' => Url::to(['/backend/equipment-part/attach']),
        'detach' => Url::to(['/backend/equipment-part/detach']),
        'income' => Url::to(['/backend/equipment-part/income']),
        'defect' => Url::to(['/backend/equipment-part/defect']),
        'writeOff' => Url::to(['/backend/equipment-part/write-off']),
    ]) . ';',
    View::POS_HEAD,
    'equipment-part-urls'
);

// Добавляем JavaScript для карусели фотографий
$carouselJs = "
$(document).ready(function() {
    // Обработчик для кнопок карусели
    $('.carousel-btn').on('click', function(e) {
        e.stopPropagation();
        e.preventDefault();
        
        var partId = $(this).data('part-id');
        var carousel = $('.photo-carousel[data-part-id=\"' + partId + '\"]');
        var photos = carousel.find('.carousel-photo');
        var currentIndex = 0;
        
        // Находим текущую видимую фотографию
        photos.each(function(index) {
            if ($(this).is(':visible')) {
                currentIndex = index;
                return false;
            }
        });
        
        var totalPhotos = photos.length;
        var newIndex;
        
        if ($(this).hasClass('carousel-next')) {
            newIndex = (currentIndex + 1) % totalPhotos;
        } else {
            newIndex = (currentIndex - 1 + totalPhotos) % totalPhotos;
        }
        
        // Скрываем все фотографии и показываем нужную
        photos.hide();
        $(photos[newIndex]).show();
        
        // Обновляем счетчик
        carousel.find('.current-photo').text(newIndex + 1);
    });
    
    // Предотвращаем клик по кнопкам от распространения на изображение
    $('.carousel-btn').on('click', function(e) {
        e.stopPropagation();
    });
    
    // Автоматическое переключение каждые 3 секунды (опционально)
    setInterval(function() {
        $('.photo-carousel').each(function() {
            var carousel = $(this);
            var photos = carousel.find('.carousel-photo');
            
            if (photos.length > 1) {
                var currentIndex = 0;
                photos.each(function(index) {
                    if ($(this).is(':visible')) {
                        currentIndex = index;
                        return false;
                    }
                });
                
                var newIndex = (currentIndex + 1) % photos.length;
                photos.hide();
                $(photos[newIndex]).show();
                carousel.find('.current-photo').text(newIndex + 1);
            }
        });
    }, 3000); // Переключение каждые 3 секунды
});
";

$this->registerJs($carouselJs, View::POS_READY, 'photo-carousel');
?>
